package com.baupos.retailmanager.customer;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.customer.domain.Gender;
import com.baupos.retailmanager.customer.dto.CreateCustomerRequestDto;
import com.baupos.retailmanager.customer.dto.CustomerDto;
import com.baupos.retailmanager.customer.dto.UpdateCustomerRequestDto;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

import static com.baupos.retailmanager.RetailManagerServerGivens.*;
import static org.junit.jupiter.api.Assertions.*;

public class CustomerControllerIT extends AbstractRetailManagerServerIT {

    @Test
    public void testCreateCustomer() {
        // Given tenant with user having CUSTOMER_CREATE privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_CREATE))
                        .build());

        // When authenticated as user with CUSTOMER_CREATE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/")
                .build();

        var request = CreateCustomerRequestDto.builder()
                .firstName(uniqueFirstName())
                .lastName(uniqueLastName())
                .email(uniqueEmailAddress())
                .phoneNumber(uniquePhoneNumber())
                .personalId("12345678")
                .taxPayerId("TAX123")
                .gender(Gender.MALE)
                .city("Test City")
                .address("123 Test Street")
                .build();

        // Then we can create a customer
        CustomerDto createdCustomer = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                CustomerDto.class);

        assertNotNull(createdCustomer);
        assertNotNull(createdCustomer.getId());
        assertEquals(request.getFirstName(), createdCustomer.getFirstName());
        assertEquals(request.getLastName(), createdCustomer.getLastName());
        assertEquals(request.getEmail(), createdCustomer.getEmail());
        assertEquals(request.getPhoneNumber(), createdCustomer.getPhoneNumber());
        assertEquals(request.getPersonalId(), createdCustomer.getPersonalId());
        assertEquals(request.getTaxPayerId(), createdCustomer.getTaxPayerId());
        assertEquals(request.getGender(), createdCustomer.getGender());
        assertEquals(request.getCity(), createdCustomer.getCity());
        assertEquals(request.getAddress(), createdCustomer.getAddress());
    }

    @Test
    public void testGetCustomerById() {
        // Given tenant with user having CUSTOMER_READ privilege and an existing customer
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_READ))
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customerContext = given().customer(
                GivenCustomerContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user with CUSTOMER_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/{id}")
                .buildAndExpand(customerContext.customer().getId());

        // Then we can retrieve the customer
        CustomerDto fetchedCustomer = restTemplate().getForObject(
                uriBuilder.toUriString(),
                CustomerDto.class);

        assertNotNull(fetchedCustomer);
        assertEquals(customerContext.customer().getId(), fetchedCustomer.getId());
        assertEquals(customerContext.customer().getFirstName(), fetchedCustomer.getFirstName());
        assertEquals(customerContext.customer().getLastName(), fetchedCustomer.getLastName());
        assertEquals(customerContext.customer().getEmail(), fetchedCustomer.getEmail());
        assertEquals(customerContext.customer().getPhoneNumber(), fetchedCustomer.getPhoneNumber());
        assertEquals(customerContext.customer().getPersonalId(), fetchedCustomer.getPersonalId());
        assertEquals(customerContext.customer().getTaxPayerId(), fetchedCustomer.getTaxPayerId());
        assertEquals(customerContext.customer().getGender(), fetchedCustomer.getGender());
        assertEquals(customerContext.customer().getCity(), fetchedCustomer.getCity());
        assertEquals(customerContext.customer().getAddress(), fetchedCustomer.getAddress());
    }

    @Test
    public void testListCustomers() {
        // Given tenant with user having CUSTOMER_READ privilege and two existing customers
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_READ))
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customer1 = given().customer(
                GivenCustomerContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customer2 = given().customer(
                GivenCustomerContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user with CUSTOMER_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/")
                .build();

        // Then we can retrieve all customers
        ResponseEntity<PageDto<CustomerDto>> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<CustomerDto>>() {});

        assertNotNull(response.getBody());
        assertTrue(response.getBody().getContent().size() >= 2);
        assertTrue(response.getBody().getContent().stream()
                .anyMatch(c -> c.getId().equals(customer1.customer().getId())));
        assertTrue(response.getBody().getContent().stream()
                .anyMatch(c -> c.getId().equals(customer2.customer().getId())));
    }

    @Test
    public void testListCustomersWithPaginationAndFiltering() {
        // Given tenant with user having CUSTOMER_READ privilege and customers with specific names
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_READ))
                        .build());

        String searchFirstName = "TestFirstName";
        RetailManagerServerGivens.GivenCustomerContext customer1 = given().customer(
                GivenCustomerContextParameters.builder()
                        .firstName(searchFirstName)
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customer2 = given().customer(
                GivenCustomerContextParameters.builder()
                        .firstName("DifferentName")
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user with CUSTOMER_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/")
                .queryParam("pageNumber", 1)
                .queryParam("pageSize", 10)
                .queryParam("sortBy", "firstName")
                .queryParam("sortDirection", "ASC")
                .queryParam("filter.firstName", searchFirstName)
                .build();

        // Then we can retrieve filtered customers
        ResponseEntity<PageDto<CustomerDto>> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<CustomerDto>>() {});

        assertNotNull(response.getBody());
        assertTrue(response.getBody().getContent().stream()
                .anyMatch(c -> c.getId().equals(customer1.customer().getId())));
        assertTrue(response.getBody().getContent().stream()
                .noneMatch(c -> c.getId().equals(customer2.customer().getId())));
    }

    @Test
    public void testUpdateCustomer() {
        // Given tenant with user having CUSTOMER_UPDATE privilege and an existing customer
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_UPDATE))
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customerContext = given().customer(
                GivenCustomerContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user with CUSTOMER_UPDATE privilege
        authenticateUser(userContext.user().getUsername());

        String newFirstName = uniqueFirstName();
        String newLastName = uniqueLastName();
        String newEmail = uniqueEmailAddress();
        String newPhoneNumber = uniquePhoneNumber();

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/{id}")
                .buildAndExpand(customerContext.customer().getId());

        var updateRequest = UpdateCustomerRequestDto.builder()
                .firstName(newFirstName)
                .lastName(newLastName)
                .email(newEmail)
                .phoneNumber(newPhoneNumber)
                .personalId("87654321")
                .taxPayerId("NEWTAX456")
                .gender(Gender.FEMALE)
                .city("New City")
                .address("456 New Street")
                .build();

        // Then we can update the customer
        CustomerDto updatedCustomer = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                CustomerDto.class)
                .getBody();

        assertNotNull(updatedCustomer);
        assertEquals(customerContext.customer().getId(), updatedCustomer.getId());
        assertEquals(newFirstName, updatedCustomer.getFirstName());
        assertEquals(newLastName, updatedCustomer.getLastName());
        assertEquals(newEmail, updatedCustomer.getEmail());
        assertEquals(newPhoneNumber, updatedCustomer.getPhoneNumber());
        assertEquals("87654321", updatedCustomer.getPersonalId());
        assertEquals("NEWTAX456", updatedCustomer.getTaxPayerId());
        assertEquals(Gender.FEMALE, updatedCustomer.getGender());
        assertEquals("New City", updatedCustomer.getCity());
        assertEquals("456 New Street", updatedCustomer.getAddress());
    }

    @Test
    public void testDeleteCustomer() {
        // Given tenant with user having CUSTOMER_DELETE privilege and an existing customer
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_DELETE))
                        .build());

        RetailManagerServerGivens.GivenCustomerContext customerContext = given().customer(
                GivenCustomerContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user with CUSTOMER_DELETE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/{id}")
                .buildAndExpand(customerContext.customer().getId());

        // Then we can delete the customer
        ResponseEntity<Void> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.DELETE,
                null,
                Void.class);

        assertEquals(200, response.getStatusCodeValue());
    }

    @Test
    public void testCreateCustomerWithMinimalData() {
        // Given tenant with user having CUSTOMER_CREATE privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.CUSTOMER_CREATE))
                        .build());

        // When authenticated as user with CUSTOMER_CREATE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/customers/")
                .build();

        // Create customer with only required fields
        var request = CreateCustomerRequestDto.builder()
                .firstName(uniqueFirstName())
                .lastName(uniqueLastName())
                .build();

        // Then we can create a customer with minimal data
        CustomerDto createdCustomer = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                CustomerDto.class);

        assertNotNull(createdCustomer);
        assertNotNull(createdCustomer.getId());
        assertEquals(request.getFirstName(), createdCustomer.getFirstName());
        assertEquals(request.getLastName(), createdCustomer.getLastName());
        assertNull(createdCustomer.getEmail());
        assertNull(createdCustomer.getPhoneNumber());
        assertNull(createdCustomer.getPersonalId());
        assertNull(createdCustomer.getTaxPayerId());
        assertNull(createdCustomer.getGender());
        assertNull(createdCustomer.getCity());
        assertNull(createdCustomer.getAddress());
    }

}
