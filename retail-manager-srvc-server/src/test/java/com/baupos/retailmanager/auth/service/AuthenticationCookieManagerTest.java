package com.baupos.retailmanager.auth.service;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Optional;

import static com.baupos.retailmanager.auth.service.AuthenticationCookieManager.ACCESS_TOKEN_COOKIE_NAME;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@ExtendWith(MockitoExtension.class)
class AuthenticationCookieManagerTest {

    private static final String ACCESS_TOKEN = "access-token";
    private static final int EXPIRES_IN = -1;
    private static final String EXPECTED_HEADER = "%s=%s; Path=/; Secure; HttpOnly; SameSite=Strict";

    private MockHttpServletRequest httpServletRequest;

    private AuthenticationCookieManager subject;

    @BeforeEach
    void setUp() {
        subject = new AuthenticationCookieManager();
        // simulate prod since other envs do not use cookies
        subject.setEnvironment("prod");
        httpServletRequest = new MockHttpServletRequest();
    }

    @Test
    void setAccessTokenCookieTest() {
        HttpServletResponse servletResponse = new MockHttpServletResponse();

        subject.setAccessTokenCookie(servletResponse, true, ACCESS_TOKEN, EXPIRES_IN);

        String setCookie = servletResponse.getHeader(HttpHeaders.SET_COOKIE);

        assertThat(setCookie).isEqualTo(String.format(EXPECTED_HEADER, ACCESS_TOKEN_COOKIE_NAME, ACCESS_TOKEN));
    }

    @Test
    void removeAccessTokenCookieTest() {
        HttpServletResponse servletResponse = new MockHttpServletResponse();

        servletResponse.addCookie(new Cookie(ACCESS_TOKEN_COOKIE_NAME, ACCESS_TOKEN));

        subject.removeCookies(servletResponse, true);
        String setCookie = servletResponse.getHeader(HttpHeaders.SET_COOKIE);

        assertThat(setCookie).doesNotContain(ACCESS_TOKEN);
    }

    @Test
    void getAccessTokenCookie() {
        final Optional<String> expected = Optional.of(ACCESS_TOKEN);

        httpServletRequest.setCookies(new Cookie(ACCESS_TOKEN_COOKIE_NAME, ACCESS_TOKEN));

        final Optional<String> actual = subject.getAccessTokenCookie(httpServletRequest);

        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void getAccessTokenCookie_withMissingCookie() {
        final Optional<String> actual = subject.getAccessTokenCookie(httpServletRequest);

        assertThat(actual).isEmpty();
    }
}
