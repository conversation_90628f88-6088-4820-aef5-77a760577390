package com.baupos.retailmanager.branch;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.branch.dto.BranchDto;
import com.baupos.retailmanager.branch.dto.CreateBranchRequestDto;
import com.baupos.retailmanager.branch.dto.UpdateBranchRequestDto;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.ResourceType;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.service.sto.GrantedResourceSto;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.UUID;

import static com.baupos.retailmanager.RetailManagerServerGivens.*;
import static org.junit.jupiter.api.Assertions.*;

public class BranchControllerIT extends AbstractRetailManagerServerIT {

    @Test
    public void testCreateBranch() {
        // Given tenant with user having BRANCH_CREATE privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_CREATE))
                        .build());

        // When authenticated as user with BRANCH_CREATE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/")
                .build();

        var request = CreateBranchRequestDto.builder()
                .name(uniqueBranchName())
                .country(uniqueCountry())
                .city(uniqueCity())
                .address(uniqueAddress())
                .phoneNumber(uniquePhoneNumber())
                .emailAddress(uniqueEmailAddress())
                .isPrimary(true)
                .build();

        // Then we can create a branch
        BranchDto createdBranch = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                BranchDto.class);

        assertNotNull(createdBranch);
        assertNotNull(createdBranch.getId());
        assertEquals(request.getName(), createdBranch.getName());
        assertEquals(request.getCountry(), createdBranch.getCountry());
        assertEquals(request.getCity(), createdBranch.getCity());
        assertEquals(request.getAddress(), createdBranch.getAddress());
        assertEquals(request.getPhoneNumber(), createdBranch.getPhoneNumber());
        assertEquals(request.getEmailAddress(), createdBranch.getEmailAddress());
        assertEquals(request.getIsPrimary(), createdBranch.getIsPrimary());
    }

    @Test
    public void testGetBranch() {
        // Given tenant with user having BRANCH_READ privilege and an existing branch
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        RetailManagerServerGivens.GivenBranchContext branchContext = given().branch(
                GivenBranchContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branchContext.branch().getId().toString())
                        ))
                        .build());

        // When authenticated as user with BRANCH_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(branchContext.branch().getId());

        // Then we can retrieve the branch
        BranchDto retrievedBranch = restTemplate().getForObject(
                uriBuilder.toUriString(),
                BranchDto.class);

        assertNotNull(retrievedBranch);
        assertEquals(branchContext.branch().getId(), retrievedBranch.getId());
        assertEquals(branchContext.branch().getName(), retrievedBranch.getName());
        assertEquals(branchContext.branch().getCountry(), retrievedBranch.getCountry());
        assertEquals(branchContext.branch().getCity(), retrievedBranch.getCity());
        assertEquals(branchContext.branch().getAddress(), retrievedBranch.getAddress());
        assertEquals(branchContext.branch().getPhoneNumber(), retrievedBranch.getPhoneNumber());
        assertEquals(branchContext.branch().getEmailAddress(), retrievedBranch.getEmailAddress());
        assertEquals(branchContext.branch().getIsPrimary(), retrievedBranch.getIsPrimary());
    }

    @Test
    public void testListBranches() {
        // Given tenant with user having BRANCH_READ privilege and two branches
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        RetailManagerServerGivens.GivenBranchContext branch1 = given().branch(
                GivenBranchContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenBranchContext branch2 = given().branch(
                GivenBranchContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branch1.branch().getId().toString()),
                                GrantedResourceSto.of(ResourceType.BRANCH, branch2.branch().getId().toString())
                        ))
                        .build());

        // When authenticated as user with BRANCH_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/")
                .build();

        // Then we can retrieve all branches
        ResponseEntity<PageDto<BranchDto>> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<BranchDto>>() {});

        assertNotNull(response.getBody());
        assertTrue(response.getBody().getContent().size() >= 2);
        
        List<String> branchIds = response.getBody().getContent().stream()
                .map(branch -> branch.getId().toString())
                .toList();
        
        assertTrue(branchIds.contains(branch1.branch().getId().toString()));
        assertTrue(branchIds.contains(branch2.branch().getId().toString()));
    }

    @Test
    public void testListBranchesWithFilters() {
        // Given tenant with user having BRANCH_READ privilege and two branches with different names
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        String searchName = "TestBranch";
        String searchCountry = "TestCountry";

        RetailManagerServerGivens.GivenBranchContext branch1 = given().branch(
                GivenBranchContextParameters.builder()
                        .name(searchName)
                        .country(searchCountry)
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenBranchContext branch2 = given().branch(
                GivenBranchContextParameters.builder()
                        .name("DifferentName")
                        .country("DifferentCountry")
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branch1.branch().getId().toString()),
                                GrantedResourceSto.of(ResourceType.BRANCH, branch2.branch().getId().toString())
                        ))
                        .build());

        // When authenticated as user with BRANCH_READ privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/")
                .queryParam("pageNumber", 1)
                .queryParam("pageSize", 10)
                .queryParam("sortBy", "name")
                .queryParam("sortDirection", "ASC")
                .queryParam("filter.name", searchName)
                .queryParam("filter.country", searchCountry)
                .build();

        // Then we can retrieve filtered branches
        ResponseEntity<PageDto<BranchDto>> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<BranchDto>>() {});

        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getContent().size());
        assertEquals(branch1.branch().getId(), response.getBody().getContent().get(0).getId());
    }

    @Test
    public void testUpdateBranch() {
        // Given tenant with user having BRANCH_UPDATE privilege and an existing branch
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        RetailManagerServerGivens.GivenBranchContext branchContext = given().branch(
                GivenBranchContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_UPDATE))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branchContext.branch().getId().toString())
                        ))
                        .build());

        // When authenticated as user with BRANCH_UPDATE privilege
        authenticateUser(userContext.user().getUsername());

        String newName = uniqueBranchName();
        String newCountry = uniqueCountry();
        String newCity = uniqueCity();
        String newAddress = uniqueAddress();
        String newPhoneNumber = uniquePhoneNumber();
        String newEmailAddress = uniqueEmailAddress();

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(branchContext.branch().getId());

        var updateRequest = UpdateBranchRequestDto.builder()
                .name(newName)
                .country(newCountry)
                .city(newCity)
                .address(newAddress)
                .phoneNumber(newPhoneNumber)
                .emailAddress(newEmailAddress)
                .isPrimary(true)
                .build();

        // Then we can update the branch
        ResponseEntity<BranchDto> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                BranchDto.class);

        assertNotNull(response.getBody());
        assertEquals(branchContext.branch().getId(), response.getBody().getId());
        assertEquals(newName, response.getBody().getName());
        assertEquals(newCountry, response.getBody().getCountry());
        assertEquals(newCity, response.getBody().getCity());
        assertEquals(newAddress, response.getBody().getAddress());
        assertEquals(newPhoneNumber, response.getBody().getPhoneNumber());
        assertEquals(newEmailAddress, response.getBody().getEmailAddress());
        assertEquals(true, response.getBody().getIsPrimary());
    }

    @Test
    public void testDeleteBranch() {
        // Given tenant with user having BRANCH_DELETE privilege and an existing branch
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        RetailManagerServerGivens.GivenBranchContext branchContext = given().branch(
                GivenBranchContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_DELETE))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branchContext.branch().getId().toString())
                        ))
                        .build());

        // When authenticated as user with BRANCH_DELETE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(branchContext.branch().getId());

        // Then we can delete the branch
        ResponseEntity<Void> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.DELETE,
                null,
                Void.class);

        assertEquals(204, response.getStatusCode().value());
    }

    @Test
    public void testCreateBranchWithMinimalData() {
        // Given tenant with user having BRANCH_CREATE privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_CREATE))
                        .build());

        // When authenticated as user with BRANCH_CREATE privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/branches/")
                .build();

        // Create branch with only required fields
        var request = CreateBranchRequestDto.builder()
                .name(uniqueBranchName())
                .country(uniqueCountry())
                .city(uniqueCity())
                .isPrimary(false)
                .build();

        // Then we can create a branch with minimal data
        BranchDto createdBranch = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                BranchDto.class);

        assertNotNull(createdBranch);
        assertNotNull(createdBranch.getId());
        assertEquals(request.getName(), createdBranch.getName());
        assertEquals(request.getCountry(), createdBranch.getCountry());
        assertEquals(request.getCity(), createdBranch.getCity());
        assertNull(createdBranch.getAddress());
        assertNull(createdBranch.getPhoneNumber());
        assertNull(createdBranch.getEmailAddress());
        assertEquals(false, createdBranch.getIsPrimary());
    }

    @Test
    public void testTenantIsolation_UserCanOnlyAccessBranchesFromOwnTenant() {
        // Given two different tenants with their own users and branches
        RetailManagerServerGivens.GivenTenantContext tenant1Context = given().tenant();
        RetailManagerServerGivens.GivenTenantContext tenant2Context = given().tenant();

        // Create a branch for tenant 1
        RetailManagerServerGivens.GivenBranchContext tenant1Branch = given().branch(
                GivenBranchContextParameters.builder()
                        .name("Tenant1 Branch")
                        .tenantId(tenant1Context.tenant().getId())
                        .build());

        // Create a branch for tenant 2
        RetailManagerServerGivens.GivenBranchContext tenant2Branch = given().branch(
                GivenBranchContextParameters.builder()
                        .name("Tenant2 Branch")
                        .tenantId(tenant2Context.tenant().getId())
                        .build());

        // User from tenant 1 with BRANCH_READ and BRANCH_UPDATE privileges
        RetailManagerServerGivens.GivenUserContext user1Context = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenant1Context.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ, Privilege.BRANCH_UPDATE))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, tenant1Branch.branch().getId().toString())
                        ))
                        .build());

        // User from tenant 2 with BRANCH_READ and BRANCH_UPDATE privileges
        RetailManagerServerGivens.GivenUserContext user2Context = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenant2Context.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ, Privilege.BRANCH_UPDATE))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, tenant2Branch.branch().getId().toString())
                        ))
                        .build());

        // Test 1: User from tenant 1 can read their own branch
        authenticateUser(user1Context.user().getUsername());

        UriComponents getBranchUri = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(tenant1Branch.branch().getId());

        BranchDto retrievedBranch = restTemplate().getForObject(
                getBranchUri.toUriString(),
                BranchDto.class);

        assertNotNull(retrievedBranch);
        assertEquals(tenant1Branch.branch().getId(), retrievedBranch.getId());
        assertEquals("Tenant1 Branch", retrievedBranch.getName());

        // Test 2: User from tenant 1 cannot read branch from tenant 2 (should get 401 Unauthorized)
        UriComponents getTenant2BranchUri = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(tenant2Branch.branch().getId());

        try {
            restTemplate().getForObject(getTenant2BranchUri.toUriString(), BranchDto.class);
            fail("Expected access to be denied for branch from different tenant");
        } catch (Exception e) {
            // Expected - should get access denied (401 Unauthorized)
            assertTrue(e.getMessage().contains("401") || e.getMessage().contains("Unauthorized") || e.getMessage().contains("Access Denied"));
        }

        // Test 3: User from tenant 1 can update their own branch
        UriComponents updateBranchUri = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(tenant1Branch.branch().getId());

        var updateRequest = UpdateBranchRequestDto.builder()
                .name("Updated Tenant1 Branch")
                .country(tenant1Branch.branch().getCountry())
                .city(tenant1Branch.branch().getCity())
                .address("Updated Address")
                .isPrimary(false)
                .build();

        ResponseEntity<BranchDto> updateResponse = restTemplate().exchange(
                updateBranchUri.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                BranchDto.class);

        assertEquals(200, updateResponse.getStatusCode().value());
        assertNotNull(updateResponse.getBody());
        assertEquals("Updated Tenant1 Branch", updateResponse.getBody().getName());
        assertEquals("Updated Address", updateResponse.getBody().getAddress());

        // Test 4: User from tenant 1 cannot update branch from tenant 2 (should get 401 Unauthorized)
        UriComponents updateTenant2BranchUri = UriComponentsBuilder.newInstance()
                .path("/v1/branches/{id}")
                .buildAndExpand(tenant2Branch.branch().getId());

        var updateTenant2Request = UpdateBranchRequestDto.builder()
                .name("Attempted Update")
                .country(tenant2Branch.branch().getCountry())
                .city(tenant2Branch.branch().getCity())
                .isPrimary(false)
                .build();

        try {
            restTemplate().exchange(
                    updateTenant2BranchUri.toUriString(),
                    HttpMethod.PUT,
                    new HttpEntity<>(updateTenant2Request),
                    BranchDto.class);
            fail("Expected access to be denied for updating branch from different tenant");
        } catch (Exception e) {
            // Expected - should get access denied (401 Unauthorized)
            assertTrue(e.getMessage().contains("401") || e.getMessage().contains("Unauthorized") || e.getMessage().contains("Access Denied"));
        }

        // Test 5: User from tenant 1 should only see branches from their tenant when listing
        UriComponents listBranchesUri = UriComponentsBuilder.newInstance()
                .path("/v1/branches/")
                .build();

        ResponseEntity<PageDto<BranchDto>> listResponse = restTemplate().exchange(
                listBranchesUri.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<BranchDto>>() {});

        assertNotNull(listResponse.getBody());
        assertTrue(listResponse.getBody().getContent().size() >= 1);

        // Verify that all returned branches belong to tenant 1 (by checking if tenant1Branch is present and tenant2Branch is not)
        List<String> branchIds = listResponse.getBody().getContent().stream()
                .map(branch -> branch.getId().toString())
                .toList();

        assertTrue(branchIds.contains(tenant1Branch.branch().getId().toString()));
        assertFalse(branchIds.contains(tenant2Branch.branch().getId().toString()));

        // Test 6: Switch to user from tenant 2 and verify they can only see their own branches
        // Clear security context before switching users to avoid tenant filter conflicts
        SecurityContextHolder.getContext().setAuthentication(null);
        authenticateUser(user2Context.user().getUsername());

        ResponseEntity<PageDto<BranchDto>> tenant2ListResponse = restTemplate().exchange(
                listBranchesUri.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<BranchDto>>() {});

        assertNotNull(tenant2ListResponse.getBody());
        assertTrue(tenant2ListResponse.getBody().getContent().size() >= 1);

        // Verify that all returned branches belong to tenant 2
        List<String> tenant2BranchIds = tenant2ListResponse.getBody().getContent().stream()
                .map(branch -> branch.getId().toString())
                .toList();

        assertTrue(tenant2BranchIds.contains(tenant2Branch.branch().getId().toString()));
        assertFalse(tenant2BranchIds.contains(tenant1Branch.branch().getId().toString()));
    }

    @Test
    public void testListBranchesFiltersByGrantedResources() {
        // Given tenant with user having BRANCH_READ privilege and three branches, but only granted access to two
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();

        RetailManagerServerGivens.GivenBranchContext branch1 = given().branch(
                GivenBranchContextParameters.builder()
                        .name("Granted Branch 1")
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenBranchContext branch2 = given().branch(
                GivenBranchContextParameters.builder()
                        .name("Granted Branch 2")
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        RetailManagerServerGivens.GivenBranchContext branch3 = given().branch(
                GivenBranchContextParameters.builder()
                        .name("Not Granted Branch")
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // User only has access to branch1 and branch2, not branch3
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.BRANCH_READ))
                        .grantedResources(List.of(
                                GrantedResourceSto.of(ResourceType.BRANCH, branch1.branch().getId().toString()),
                                GrantedResourceSto.of(ResourceType.BRANCH, branch2.branch().getId().toString())
                                // Note: branch3 is NOT granted
                        ))
                        .build());

        // When authenticated as user with limited branch access
        authenticateUser(userContext.user().getUsername());

        // When listing branches
        ResponseEntity<PageDto<BranchDto>> response = restTemplate().exchange(
                "/v1/branches/",
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<BranchDto>>() {}
        );

        // Then only granted branches are returned
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(2, response.getBody().getTotalElements());

        List<UUID> returnedBranchIds = response.getBody().getContent().stream()
                .map(BranchDto::getId)
                .toList();

        assertTrue(returnedBranchIds.contains(branch1.branch().getId()));
        assertTrue(returnedBranchIds.contains(branch2.branch().getId()));
        assertFalse(returnedBranchIds.contains(branch3.branch().getId()));
    }

}
