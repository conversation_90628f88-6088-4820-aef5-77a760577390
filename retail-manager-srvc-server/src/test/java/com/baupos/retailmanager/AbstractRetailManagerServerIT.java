package com.baupos.retailmanager;

import com.baupos.retailmanager.auth.service.AccessTokenAuthentication;
import com.baupos.retailmanager.auth.service.AuthUserDetailsService;
import com.baupos.retailmanager.auth.service.JwtUtil;
import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.common.utils.RestTemplateCredentialsPropagationInterceptor;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;
import software.amazon.awssdk.services.s3.S3Client;

import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.baupos.retailmanager.common.config.RetailManagerServerConfiguration.DEFAULT_CLOCK_OFFSET;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {
		RetailManagerServer.class, RetailManagerServerGivens.class
})
@ActiveProfiles({"test"})
@Accessors(fluent = true)
public class AbstractRetailManagerServerIT implements InitializingBean {
	@LocalServerPort
	private int port;
	@Value("${server.servlet.context-path}")
	private String contextPath;

	@Getter
	private RestTemplate restTemplate;

	@Getter
	@Autowired
	private RetailManagerServerGivens given;

	@Autowired
	private AuthUserDetailsService authUserDetailsService;

	@Autowired
	private JwtUtil jwtUtil;

	@Getter
	@MockBean(name = "clock")
	private Clock clock;

	@Getter
	@MockBean
	private S3Client s3Client;

	@Override
	public void afterPropertiesSet() {
		restTemplate = buildRestTemplate(getBaseUrl());
	}

	protected String getBaseUrl() {
		return "http://localhost:" + port + contextPath;
	}

	private RestTemplate buildRestTemplate(String baseUrl) {
		RestTemplate restTemplate = new RestTemplate();
		restTemplate.setUriTemplateHandler(new DefaultUriBuilderFactory(baseUrl));
		restTemplate.getInterceptors().add(new RestTemplateCredentialsPropagationInterceptor());
		return restTemplate;
	}

	protected void authenticateUser(String username) {
		AuthUserDetailsSto userDetails = authUserDetailsService.loadUserByUsername(username);
		String accessToken = jwtUtil.generateToken(userDetails);

		AccessTokenAuthentication authentication = new AccessTokenAuthentication(userDetails, null, userDetails.getAuthorities(), accessToken);
		SecurityContextHolder.getContext().setAuthentication(authentication);
	}

	@BeforeEach
	public void initClockMock() {
		LocalDate now = LocalDate.now();
		Instant instant = now.atStartOfDay(DEFAULT_CLOCK_OFFSET).toInstant();

		when(clock.instant()).thenReturn(instant);
		when(clock.getZone()).thenReturn(DEFAULT_CLOCK_OFFSET);
	}

	protected void givenClockAtStartOfDayOn(LocalDate date) {
		Instant instant = date.atStartOfDay(DEFAULT_CLOCK_OFFSET).toInstant();
		doReturn(instant).when(clock).instant();
		doReturn(DEFAULT_CLOCK_OFFSET).when(clock).getZone();
	}

	protected void mockNowTime(LocalDateTime nowTime) {
		final Clock scenarioClock = Clock.fixed(nowTime.atZone(DEFAULT_CLOCK_OFFSET).toInstant(), DEFAULT_CLOCK_OFFSET);
		doReturn(scenarioClock.instant()).when(clock).instant();
		doReturn(scenarioClock.getZone()).when(clock).getZone();
	}
}
