package com.baupos.retailmanager;

import com.github.javafaker.Faker;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.tenant.domain.*;
import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import com.baupos.retailmanager.tenant.service.TenantMapper;
import com.baupos.retailmanager.tenant.service.TenantService;
import com.baupos.retailmanager.tenant.service.TenantSubscriptionMapper;
import com.baupos.retailmanager.tenant.service.sto.TenantSto;
import com.baupos.retailmanager.tenant.service.sto.TenantSubscriptionSto;
import com.baupos.retailmanager.user.domain.*;
import com.baupos.retailmanager.user.service.UserMapper;
import com.baupos.retailmanager.user.service.sto.GrantedResourceSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import com.baupos.retailmanager.employee.domain.Employee;
import com.baupos.retailmanager.employee.domain.EmployeeRepository;
import com.baupos.retailmanager.employee.service.EmployeeMapper;
import com.baupos.retailmanager.employee.service.sto.EmployeeSto;
import com.baupos.retailmanager.customer.domain.Customer;
import com.baupos.retailmanager.customer.domain.CustomerRepository;
import com.baupos.retailmanager.customer.domain.Gender;
import com.baupos.retailmanager.customer.service.CustomerMapper;
import com.baupos.retailmanager.customer.service.sto.CustomerSto;
import com.baupos.retailmanager.branch.domain.Branch;
import com.baupos.retailmanager.branch.domain.BranchRepository;
import com.baupos.retailmanager.branch.service.BranchMapper;
import com.baupos.retailmanager.branch.service.sto.BranchSto;
import jakarta.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.mockito.ArgumentMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import static org.mockito.ArgumentMatchers.argThat;

@Component
public class RetailManagerServerGivens {

    private static final Faker FAKER = new Faker();
    private static final AtomicLong ATOMIC_LONG = new AtomicLong(10000);

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TenantRepository tenantRepository;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private TenantMapper tenantMapper;
    @Autowired
    private TenantSubscriptionMapper tenantSubscriptionMapper;
    @Autowired
    private TenantSubscriptionRepository tenantSubscriptionRepository;
    @Autowired
    private TenantInvoiceRepository tenantInvoiceRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private BranchRepository branchRepository;
    @Autowired
    private BranchMapper branchMapper;

    public static String uniqueFullName() {
        return FAKER.name().fullName();
    }

    public static String uniqueFirstName() {
        return FAKER.name().firstName();
    }

    public static String uniqueLastName() {
        return FAKER.name().lastName();
    }

    public static String uniquePhoneNumber() {
        return FAKER.phoneNumber().cellPhone();
    }

    public static String uniqueEmailAddress() {
        return FAKER.internet().emailAddress();
    }

    public static String uniqueBranchName() {
        return FAKER.company().name() + " Branch";
    }

    public static String uniqueCountry() {
        return FAKER.address().country();
    }

    public static String uniqueCity() {
        return FAKER.address().city();
    }

    public static String uniqueAddress() {
        return FAKER.address().streetAddress();
    }

    public static <O> O requestThat(Consumer<O> captor, ArgumentMatcher<O> matcher) {
        return argThat(argument -> {
            if (matcher.matches(argument)) {
                captor.accept(argument);
                return true;
            } else {
                return false;
            }
        });
    }

    public static <T> Consumer<T> noop() {
        return v -> {
        };
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenUserContextParameters {
        @Builder.Default
        private String username = FAKER.internet().emailAddress();
        @Builder.Default
        private String password = FAKER.internet().password();
        @Builder.Default
        private Role role = Role.EMPLOYEE;
        private List<Privilege> privileges;
        private List<GrantedResourceSto> grantedResources;

        @Nullable
        private UUID tenantId;
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenUserContext {
        private UserSto user;
    }

    @Transactional
    public GivenUserContext user() {
        return user(GivenUserContextParameters.builder().build());
    }

    @Transactional
    public GivenUserContext user(GivenUserContextParameters params) {
        UUID tenantId = Objects.requireNonNullElse(params.tenantId(), tenant().tenant.getId());
        User user = User.builder()
                .username(params.username())
                .password(params.password())
                .role(params.role())
                .tenantId(tenantId)
                .build();

        Objects.requireNonNullElse(params.privileges(), new ArrayList<Privilege>()).stream()
                .forEach(p -> user.addPrivilege(UserPrivilege.builder()
                        .privilege(p)
                        .build()));

        Objects.requireNonNullElse(params.grantedResources(), new ArrayList<GrantedResourceSto>()).stream()
                .forEach(gr -> user.addGrantedResource(UserGrantedResource.builder()
                        .type(gr.getType())
                        .key(gr.getKey())
                        .build()));

        User newUser = userRepository.save(user);
        return GivenUserContext.builder()
                .user(userMapper.map(newUser))
                .build();
    }


    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenTenantContextParameters {
        private TenantSubscriptionStatus subscriptionStatus;
        private OffsetDateTime subscriptionCreateDate;
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenTenantContext {
        private TenantSto tenant;
        private List<TenantSubscriptionSto> subscriptions;
    }

    @Transactional
    public GivenTenantContext tenant() {
        return tenant(GivenTenantContextParameters.builder().build());
    }

    @Transactional
    public GivenTenantContext tenant(GivenTenantContextParameters params) {
        TenantSto tenantSto = tenantService.createTenantAndTrialSubscription();

        List<TenantSubscriptionSto> tenantSubscriptions = tenantSubscriptionRepository.findByTenantId(tenantSto.getId())
                .stream()
                .map(s -> {
                    if (params.subscriptionStatus() != null) {
                        s.setStatus(params.subscriptionStatus());
                        s = tenantSubscriptionRepository.save(s);
                    }
                    return tenantSubscriptionMapper.mapToSto(s);
                })
                .toList();

        return GivenTenantContext.builder()
                .tenant(tenantSto)
                .subscriptions(tenantSubscriptions)
                .build();
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenTenantInvoiceContextParameters {
        private UUID tenantId;
        private UUID subscriptionId;
        private TenantInvoiceStatus status;
        @Builder.Default
        private OffsetDateTime issueDate = OffsetDateTime.now();
        @Builder.Default
        private OffsetDateTime dueDate = OffsetDateTime.now().plusDays(15);
    }

    @Transactional
    public void tenantInvoice(GivenTenantInvoiceContextParameters parameters) {
        tenantInvoiceRepository.save(TenantInvoice.builder()
                .tenantId(parameters.tenantId())
                .subscription(TenantSubscription.builder().id(parameters.subscriptionId()).build())
                .status(parameters.status())
                .issueDate(parameters.issueDate())
                .dueDate(parameters.dueDate())
                .build());
    }
    
    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenEmployeeContextParameters {
        @Builder.Default
        private String firstName = uniqueFirstName();
        @Builder.Default
        private String lastName = uniqueLastName();
        @Builder.Default
        private String phoneNumber = uniquePhoneNumber();
        @Builder.Default
        private String email = uniqueEmailAddress();
        private boolean systemAccess;
        private List<Privilege> privileges;
        
        @Nullable
        private UUID tenantId;
        @Nullable
        private UUID userId;
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenEmployeeContext {
        private EmployeeSto employee;
        private UserSto user;
    }

    @Transactional
    public GivenEmployeeContext employee() {
        return employee(GivenEmployeeContextParameters.builder().build());
    }

    @Transactional
    public GivenEmployeeContext employee(GivenEmployeeContextParameters params) {
        UUID tenantId = Objects.requireNonNullElse(params.tenantId(), tenant().tenant.getId());
        
        Employee employee = Employee.builder()
                .firstName(params.firstName())
                .lastName(params.lastName())
                .phoneNumber(params.phoneNumber())
                .email(params.email())
                .tenantId(tenantId)
                .build();
        
        if (params.systemAccess()) {
            User user;
            if (params.userId() != null) {
                user = userRepository.getReferenceById(params.userId());
            } else {
                GivenUserContext userContext = user(GivenUserContextParameters.builder()
                        .privileges(params.privileges())
                        .build());
                user = userRepository.getReferenceById(userContext.user.getId());
            }
            employee.setUser(user);
        }
        
        Employee savedEmployee = employeeRepository.save(employee);
        
        return GivenEmployeeContext.builder()
                .employee(employeeMapper.map(savedEmployee))
                .user(savedEmployee.getUser() != null ? userMapper.map(savedEmployee.getUser()) : null)
                .build();
    }
    
    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenCustomerContextParameters {
        @Builder.Default
        private String firstName = uniqueFirstName();
        @Builder.Default
        private String lastName = uniqueLastName();
        @Builder.Default
        private String email = uniqueEmailAddress();
        @Builder.Default
        private String phoneNumber = uniquePhoneNumber();
        private String personalId;
        private String taxPayerId;
        @Builder.Default
        private Gender gender = Gender.MALE;
        private String city;
        private String address;
        
        @Nullable
        private UUID tenantId;
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenCustomerContext {
        private CustomerSto customer;
    }

    @Transactional
    public GivenCustomerContext customer() {
        return customer(GivenCustomerContextParameters.builder().build());
    }

    @Transactional
    public GivenCustomerContext customer(GivenCustomerContextParameters params) {
        UUID tenantId = Objects.requireNonNullElse(params.tenantId(), tenant().tenant.getId());
        
        Customer customer = Customer.builder()
                .firstName(params.firstName())
                .lastName(params.lastName())
                .email(params.email())
                .phoneNumber(params.phoneNumber())
                .personalId(params.personalId())
                .taxPayerId(params.taxPayerId())
                .gender(params.gender())
                .city(params.city())
                .address(params.address())
                .tenantId(tenantId)
                .build();
        
        Customer savedCustomer = customerRepository.save(customer);
        
        return GivenCustomerContext.builder()
                .customer(customerMapper.map(savedCustomer))
                .build();
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenBranchContextParameters {
        @Builder.Default
        private String name = uniqueBranchName();
        @Builder.Default
        private String country = uniqueCountry();
        @Builder.Default
        private String city = uniqueCity();
        @Builder.Default
        private String address = uniqueAddress();
        @Builder.Default
        private String phoneNumber = uniquePhoneNumber();
        @Builder.Default
        private String emailAddress = uniqueEmailAddress();
        @Builder.Default
        private Boolean isPrimary = false;

        @Nullable
        private UUID tenantId;
    }

    @Builder
    @Getter
    @Accessors(fluent = true)
    public static class GivenBranchContext {
        private BranchSto branch;
    }

    @Transactional
    public GivenBranchContext branch() {
        return branch(GivenBranchContextParameters.builder().build());
    }

    @Transactional
    public GivenBranchContext branch(GivenBranchContextParameters params) {
        UUID tenantId = Objects.requireNonNullElse(params.tenantId(), tenant().tenant.getId());

        Branch branch = Branch.builder()
                .name(params.name())
                .country(params.country())
                .city(params.city())
                .address(params.address())
                .phoneNumber(params.phoneNumber())
                .emailAddress(params.emailAddress())
                .isPrimary(params.isPrimary())
                .tenantId(tenantId)
                .build();

        Branch savedBranch = branchRepository.save(branch);

        return GivenBranchContext.builder()
                .branch(branchMapper.map(savedBranch))
                .build();
    }

}
