package com.baupos.retailmanager.tenant.job;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.tenant.domain.TenantSubscriptionRepository;
import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.OffsetDateTime;

import static com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus.ACTIVE;
import static com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus.SUSPENDED;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class TenantOverdueSubscriptionSuspensionJobIT extends AbstractRetailManagerServerIT {

    @Autowired
    private TenantOverdueSubscriptionSuspensionJob tenantOverdueSubscriptionSuspensionJob;
    @Autowired
    private TenantSubscriptionRepository tenantSubscriptionRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;

    @Test
    public void suspendOverdueSubscriptions() {
        RetailManagerServerGivens.GivenTenantContext activeSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.ACTIVE)
                .build());
        RetailManagerServerGivens.GivenTenantContext activeOverdueSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.ACTIVE)
                .build());

        // Given overdue invoice
        given().tenantInvoice(RetailManagerServerGivens.GivenTenantInvoiceContextParameters.builder()
                        .tenantId(activeOverdueSubscriptionTenant.tenant().getId())
                        .subscriptionId(activeOverdueSubscriptionTenant.subscriptions().get(0).getId())
                        .status(TenantInvoiceStatus.PAYMENT_PENDING)
                        .dueDate(OffsetDateTime.now().minusDays(20))
                .build());

        // Given job runs
        tenantOverdueSubscriptionSuspensionJob.suspendOverdueSubscriptions();

        // Verify subscription was suspended for tenant with overdue invoice and untouched for active tenant
        transactionTaskRunner.readOnly(() -> {
            var overDueSubscription = tenantSubscriptionRepository.findByTenantId(activeOverdueSubscriptionTenant.tenant().getId()).get(0);
            assertThat(overDueSubscription.getStatus()).isEqualTo(SUSPENDED);

            var activeSubscription = tenantSubscriptionRepository.findByTenantId(activeSubscriptionTenant.tenant().getId()).get(0);
            assertThat(activeSubscription.getStatus()).isEqualTo(ACTIVE);
        });

    }
}
