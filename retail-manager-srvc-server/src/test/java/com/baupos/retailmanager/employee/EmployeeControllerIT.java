package com.baupos.retailmanager.employee;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.error.ErrorResponse;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.employee.dto.CreateEmployeeRequestDto;
import com.baupos.retailmanager.employee.dto.EmployeeDto;
import com.baupos.retailmanager.employee.dto.UpdateEmployeeRequestDto;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.domain.UserRepository;
import com.baupos.retailmanager.user.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Set;

import static com.baupos.retailmanager.RetailManagerServerGivens.*;
import static com.baupos.retailmanager.common.error.CmErrorCode.MISSING_ARGUMENTS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

public class EmployeeControllerIT extends AbstractRetailManagerServerIT {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;

    @Test
    public void testCreateEmployeeNoSystemAccess() {
        // Given tenant with owner user
        RetailManagerServerGivens.GivenTenantContext tenantContext1 = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext1FromTenant1 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext1.tenant().getId())
                        .build());

        // When authenticated as owner
        authenticateUser(userContext1FromTenant1.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/employees/")
                .build();

        var request = CreateEmployeeRequestDto.builder()
                .firstName(uniqueFirstName())
                .lastName(uniqueLastName())
                .phoneNumber(uniquePhoneNumber())
                .email(uniqueEmailAddress())
                .build();

        EmployeeDto response = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                EmployeeDto.class);

        assertThat(response.getFirstName()).isEqualTo(request.getFirstName());
        assertThat(response.getLastName()).isEqualTo(request.getLastName());
        assertThat(response.getPhoneNumber()).isEqualTo(request.getPhoneNumber());
        assertThat(response.getEmail()).isEqualTo(request.getEmail());
        assertThat(response.getUser()).isNull();
    }

    @Test
    public void testCreateEmployeeWithSystemAccessMissingArgsThrows() {
        // Given tenant with owner user
        RetailManagerServerGivens.GivenTenantContext tenantContext1 = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext1FromTenant1 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext1.tenant().getId())
                        .build());

        // When authenticated as owner
        authenticateUser(userContext1FromTenant1.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/employees/")
                .build();

        var request = CreateEmployeeRequestDto.builder()
                .firstName(uniqueFirstName())
                .lastName(uniqueLastName())
                .phoneNumber(uniquePhoneNumber())
                .email(uniqueEmailAddress())
                .systemAccess(true)
                .build();

        assertThatExceptionOfType(HttpClientErrorException.class)
                .isThrownBy(() -> restTemplate().postForObject(uriBuilder.toUriString(), request, EmployeeDto.class))
                .satisfies(e -> {
                    ErrorResponse response = e.getResponseBodyAs(ErrorResponse.class);
                    assertThat(response.getCodeName()).isEqualTo(MISSING_ARGUMENTS.name());
                });
    }

    @Test
    public void testCreateEmployeeWithSystemAccess() {
        // Given tenant with owner user
        RetailManagerServerGivens.GivenTenantContext tenantContext1 = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext1FromTenant1 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext1.tenant().getId())
                        .build());

        // When authenticated as owner
        authenticateUser(userContext1FromTenant1.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/employees/")
                .build();

        var request = CreateEmployeeRequestDto.builder()
                .firstName(uniqueFirstName())
                .lastName(uniqueLastName())
                .phoneNumber(uniquePhoneNumber())
                .email(uniqueEmailAddress())
                .systemAccess(true)
                .password("Welcome@123")
                .privileges(Set.of(Privilege.EMPLOYEE_READ))
                .build();

        EmployeeDto response = restTemplate().postForObject(
                uriBuilder.toUriString(),
                request,
                EmployeeDto.class);

        assertThat(response.getFirstName()).isEqualTo(request.getFirstName());
        assertThat(response.getLastName()).isEqualTo(request.getLastName());
        assertThat(response.getPhoneNumber()).isEqualTo(request.getPhoneNumber());
        assertThat(response.getEmail()).isEqualTo(request.getEmail());
        assertThat(response.getUser()).isNotNull();
        assertThat(response.getUser().getUsername()).isEqualTo(request.getEmail());
        assertThat(response.getUser().getPrivileges()).containsExactly(Privilege.EMPLOYEE_READ);
        assertThat(response.getUser().getRole()).isEqualTo(Role.EMPLOYEE);
    }

    @Test
    public void testListEmployees() {
        // Given tenant with owner user and two employees
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext ownerUser = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.EMPLOYEE_READ))
                        .build());
        
        RetailManagerServerGivens.GivenEmployeeContext employee1 = given().employee(
                RetailManagerServerGivens.GivenEmployeeContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());
        
        RetailManagerServerGivens.GivenEmployeeContext employee2 = given().employee(
                RetailManagerServerGivens.GivenEmployeeContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as owner
        authenticateUser(ownerUser.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/employees/")
                .build();

        // Then we can retrieve all employees
        ResponseEntity<PageDto<EmployeeDto>> response = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<EmployeeDto>>() {});

        List<EmployeeDto> employees = response.getBody().getContent();
        assertThat(employees).hasSize(2);
        assertThat(employees.stream().map(EmployeeDto::getId))
                .containsExactlyInAnyOrder(employee1.employee().getId(), employee2.employee().getId());
    }

    @Test
    public void testUpdateEmployee() {
        // Given tenant with owner user and an employee
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext ownerUser = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext.tenant().getId())
                        .build());
        
        RetailManagerServerGivens.GivenEmployeeContext employeeContext = given().employee(
                RetailManagerServerGivens.GivenEmployeeContextParameters.builder()
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as owner
        authenticateUser(ownerUser.user().getUsername());

        String newFirstName = uniqueFirstName();
        String newLastName = uniqueLastName();
        String newPhoneNumber = uniquePhoneNumber();
        String newEmail = uniqueEmailAddress();

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/employees/{id}")
                .buildAndExpand(employeeContext.employee().getId());

        var updateRequest = new UpdateEmployeeRequestDto(
                newFirstName,
                newLastName,
                newPhoneNumber,
                newEmail);

        // Then we can update the employee
        EmployeeDto updatedEmployee = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                EmployeeDto.class)
                .getBody();

        assertThat(updatedEmployee.getId()).isEqualTo(employeeContext.employee().getId());
        assertThat(updatedEmployee.getFirstName()).isEqualTo(newFirstName);
        assertThat(updatedEmployee.getLastName()).isEqualTo(newLastName);
        assertThat(updatedEmployee.getPhoneNumber()).isEqualTo(newPhoneNumber);
        assertThat(updatedEmployee.getEmail()).isEqualTo(newEmail);
    }
}
