package com.baupos.retailmanager.user;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.domain.UserRepository;
import com.baupos.retailmanager.user.dto.GrantedResourceDto;
import com.baupos.retailmanager.user.dto.UserDto;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.GrantedResourceSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

public class UserControllerIT extends AbstractRetailManagerServerIT {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;
    //
    // Get user details tests
    //

    @Test
    public void testGetUserDetailsSelfOk() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        authenticateUser(existingUserContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        UserDto fetchedUser = restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class);
        assertUserDetailsMatch(fetchedUser, existingUserContext.user());
    }

    @Test
    public void testGetUserDetailsWhenAuthenticatedAsDifferentUserWithinSameTenantAndRightPrivilegeOk() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        RetailManagerServerGivens.GivenUserContext existingUserContext2 = given().user(RetailManagerServerGivens.GivenUserContextParameters
                .builder()
                .tenantId(existingUserContext.user().getTenantId())
                .privileges(List.of(Privilege.USER_READ))
                .build());

        authenticateUser(existingUserContext2.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        UserDto fetchedUser = restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class);
        assertUserDetailsMatch(fetchedUser, existingUserContext.user());
    }

    @Test
    public void testGetUserDetailsAuthenticatedAsOwnerOk() {
        RetailManagerServerGivens.GivenUserContext ownerUserContext = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .build());

        RetailManagerServerGivens.GivenUserContext employeeUserContext = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .tenantId(ownerUserContext.user().getTenantId())
                        .build());

        authenticateUser(ownerUserContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(employeeUserContext.user().getId());

        UserDto fetchedUser = restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class);
        assertUserDetailsMatch(fetchedUser, employeeUserContext.user());
    }

    @Test
    public void testGetUserDetailsWhenNotAuthenticatedFails() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        assertThatExceptionOfType(HttpClientErrorException.class).isThrownBy(() ->
                        restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class))
                .satisfies(e -> assertThat(e.getStatusCode()).isEqualTo(HttpStatusCode.valueOf(401)));
    }

    @Test
    public void testGetUserDetailsWhenAuthenticatedAsDifferentUserWithinSameTenantButNoPrivilegeFails() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        RetailManagerServerGivens.GivenUserContext existingUserContext2 = given().user(RetailManagerServerGivens.GivenUserContextParameters
                .builder()
                .tenantId(existingUserContext.user().getTenantId())
                .build());

        authenticateUser(existingUserContext2.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        assertThatExceptionOfType(HttpClientErrorException.class).isThrownBy(() ->
                        restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class))
                .satisfies(e -> assertThat(e.getStatusCode()).isEqualTo(HttpStatusCode.valueOf(401)));
    }

    @Test
    public void testGetUserDetailsWhenAuthenticatedAsDifferentUserFromDifferentTenantAndRightPrivilegeFails() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        RetailManagerServerGivens.GivenUserContext existingUserContext2 = given().user(RetailManagerServerGivens.GivenUserContextParameters
                .builder()
                .privileges(List.of(Privilege.USER_READ))
                .build());

        authenticateUser(existingUserContext2.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        assertThatExceptionOfType(HttpClientErrorException.class).isThrownBy(() ->
                        restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class))
                .satisfies(e -> assertThat(e.getStatusCode()).isEqualTo(HttpStatusCode.valueOf(401)));
    }

    @Test
    public void testGetSoftDeletedUserIsNotFound() {
        // Given soft deleted user
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        userService.deleteUser(existingUserContext.user().getId());

        transactionTaskRunner.readOnly(() -> {
            var user = userRepository.findById(existingUserContext.user().getId());
            assertThat(user.get().getDeleteDate()).isNotNull();
        });

        // Given owner user authentication context
        RetailManagerServerGivens.GivenUserContext ownerUser = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(existingUserContext.user().getTenantId())
                        .build());
        authenticateUser(ownerUser.user().getUsername());

        // Fetching soft deleted user returns 404
        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/{userId}")
                .buildAndExpand(existingUserContext.user().getId());

        assertThatExceptionOfType(HttpClientErrorException.class)
                .isThrownBy(() -> restTemplate().getForObject(uriBuilder.toUriString(), UserDto.class))
                .satisfies(e -> e.getStatusCode().isSameCodeAs(HttpStatusCode.valueOf(404)));
    }

    //
    // List users tests
    //

    @Test
    public void testThatUsersListShowsElementsFromAuthenticatedTenantOnly() {
        // Given tenant 1 with two users
        RetailManagerServerGivens.GivenTenantContext tenantContext1 = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext1FromTenant1 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext1.tenant().getId())
                        .build());
        RetailManagerServerGivens.GivenUserContext userContext2FromTenant1 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .tenantId(tenantContext1.tenant().getId())
                        .build());

        // and a soft deleted user
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                .tenantId(tenantContext1.tenant().getId())
                .build());
        userService.deleteUser(existingUserContext.user().getId());

        // Given another tenant 2 with two users
        RetailManagerServerGivens.GivenTenantContext tenantContext2 = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext1FromTenant2 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .role(Role.OWNER)
                        .tenantId(tenantContext2.tenant().getId())
                        .build());
        RetailManagerServerGivens.GivenUserContext userContext2FromTenant2 = given()
                .user(RetailManagerServerGivens.GivenUserContextParameters.builder()
                        .tenantId(tenantContext2.tenant().getId())
                        .build());

        // When authenticated as owner of tenant 1
        authenticateUser(userContext1FromTenant1.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/users/")
                .build();

        ResponseEntity<PageDto<UserDto>> fetchedUsers = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageDto<UserDto>>() {});

        List<UserDto> fetchedUsersList = fetchedUsers.getBody().getContent();
        assertThat(fetchedUsersList).hasSize(2);
        assertThat(fetchedUsersList.stream().map(UserDto::getId))
                .containsExactlyInAnyOrderElementsOf(Stream.of(userContext1FromTenant1, userContext2FromTenant1)
                        .map(RetailManagerServerGivens.GivenUserContext::user)
                        .map(UserSto::getId)
                        .toList());
    }

    public void assertUserDetailsMatch(UserDto userResponse, UserSto user) {
        assertThat(userResponse.getId()).isEqualTo(user.getId());
        assertThat(userResponse.getUsername()).isEqualTo(user.getUsername());
        assertThat(userResponse.getRole()).isEqualTo(user.getRole());
        assertThat(userResponse.getPrivileges()).containsExactlyElementsOf(user.getPrivileges());
        assertThat(userResponse.getGrantedResources().stream().map(GrantedResourceDto::toString).toList())
                .containsExactlyElementsOf(user.getGrantedResources().stream().map(GrantedResourceSto::toString).toList());
    }
}
