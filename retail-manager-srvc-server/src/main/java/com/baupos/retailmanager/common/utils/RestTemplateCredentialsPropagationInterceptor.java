package com.baupos.retailmanager.common.utils;

import com.baupos.retailmanager.auth.service.AccessTokenAuthentication;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.IOException;

public class RestTemplateCredentialsPropagationInterceptor implements ClientHttpRequestInterceptor {
    private static final String BEARER_TYPE = "Bearer";

    public RestTemplateCredentialsPropagationInterceptor() {
    }

    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        if (!CollectionUtils.isEmpty(request.getHeaders().get("Authorization"))) {
            return execution.execute(request, body);
        } else {
            Authentication currentAuthentication = SecurityContextHolder.getContext().getAuthentication();
            if (currentAuthentication instanceof AccessTokenAuthentication && currentAuthentication.isAuthenticated()) {
                String accessToken = ((AccessTokenAuthentication) currentAuthentication).getAccessToken();
                String authorizationHeader = "Bearer " + accessToken;
                request.getHeaders().set("Authorization", authorizationHeader);
                return execution.execute(request, body);
            } else {
                return execution.execute(request, body);
            }
        }
    }
}