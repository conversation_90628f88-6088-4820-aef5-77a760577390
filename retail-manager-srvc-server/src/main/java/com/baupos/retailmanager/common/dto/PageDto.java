package com.baupos.retailmanager.common.dto;

import lombok.*;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageDto<E> {
    private List<E> content;
    private int pageSize;
    private int pageNumber;
    private int pageElements;
    private long totalElements;
    private int totalPages;

    public <T> PageDto(Page<T> pageResult, Function<T, E> mapperFunc){
        this.setContent(pageResult.getContent().stream().map(mapperFunc).collect(Collectors.toList()));
        this.setPageSize(pageResult.getSize());
        this.setPageNumber(pageResult.getPageable().getPageNumber()+1);
        this.setPageElements(pageResult.getNumberOfElements());
        this.setTotalElements(pageResult.getTotalElements());
        this.setTotalPages(pageResult.getTotalPages());
    }
}