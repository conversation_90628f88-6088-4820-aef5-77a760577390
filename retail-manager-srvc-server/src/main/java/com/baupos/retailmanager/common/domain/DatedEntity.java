package com.baupos.retailmanager.common.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.OffsetDateTime;

/**
 * Base entity with create and update date fields + version field for optimistic locking
 */
@SuperBuilder(toBuilder = true)
@MappedSuperclass
@Getter
@Access(AccessType.FIELD)
public abstract class DatedEntity {
    @Column(nullable = false, updatable = false, name = "create_date")
    private OffsetDateTime createDate;

    @Setter
    @Column(nullable = false, name = "update_date")
    private OffsetDateTime updateDate;

    @Column(nullable = false)
    @Version
    private long version;

    protected DatedEntity() {
        version = 0;
    }

    @PrePersist
    protected void onCreate() {
        createDate = OffsetDateTime.now();
        updateDate = createDate;
    }
   
    @PreUpdate
    protected void onUpdate() {
        updateDate = OffsetDateTime.now();
    }
}