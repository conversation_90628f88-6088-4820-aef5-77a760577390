package com.baupos.retailmanager.common.config;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import lombok.RequiredArgsConstructor;
import org.checkerframework.checker.initialization.qual.Initialized;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.UnknownKeyFor;
import org.hibernate.annotations.TenantId;
import org.hibernate.cfg.AvailableSettings;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Provides a way for Hibernate to dynamically determine the identifier
 * of the current tenant at runtime. The tenant identifier is extracted
 * from the authorization context.
 * Hibernate uses the tenant identifier to automatically restrict
 * the data accessible during the request/session, for doing this,
 * Hibernate also looks for entity fields annotated with {@link TenantId}
 * to ensure that the value matches the tenant identifier resolved here.
 *
 * When no tenant identifier is found on the auth context (no user logged in),
 * the {@link #ROOT_TENANT_ID} is used instead. When this root tenant identifier
 * is used, hibernate allows accessing / writing rows from any tenant.
 */
@Component
@RequiredArgsConstructor
public class TenantIdentifierResolver implements CurrentTenantIdentifierResolver<UUID>, HibernatePropertiesCustomizer {

    private static final UUID ROOT_TENANT_ID = new UUID(0,0);
    private final AuthenticationFacade authenticationFacade;

    @Override
    public UUID resolveCurrentTenantIdentifier() {
        return authenticationFacade.getAuthenticatedTenantId().orElse(ROOT_TENANT_ID);
    }

    @Override
    public @UnknownKeyFor @NonNull @Initialized boolean validateExistingCurrentSessions() {
        return false;
    }

    @Override
    public void customize(Map<String, Object> hibernateProperties) {;
        hibernateProperties.put(AvailableSettings.MULTI_TENANT_IDENTIFIER_RESOLVER, this);
    }

    /**
     * Used by hibernate to determine if the resolved tenant id should be
     * granted access to all rows or if filtering should be applied.
     * @param tenantId
     * @return
     */
    public boolean isRoot(UUID tenantId) {
        return Objects.equals(tenantId, ROOT_TENANT_ID);
    }

}