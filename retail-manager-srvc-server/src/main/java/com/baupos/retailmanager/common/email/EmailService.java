package com.baupos.retailmanager.common.email;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EmailService {

    private static final String PASSWORD_RESET_EMAIL_SUBJECT = "Código de cambio de contraseña";
    private static final String PASSWORD_RESET_EMAIL_BODY = "Tu código de cambio de contraseña es %s";

    @Value("${email.from.address}")
    private String emailFromAddress;

    private final JavaMailSender javaMailSender;

    public void sendEmail(String to, String subject, String body) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(emailFromAddress);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(body);

        javaMailSender.send(message);
    }

    public void sendPasswordResetToken(String email, String token) {
        sendEmail(email, PASSWORD_RESET_EMAIL_SUBJECT, String.format(PASSWORD_RESET_EMAIL_BODY, token));
    }
}
