package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.ErrorDetails;
import com.baupos.retailmanager.common.error.ErrorType;
import com.baupos.retailmanager.common.error.IErrorCode;
import lombok.Getter;
import org.springframework.http.HttpStatus;

import java.util.Map;

@Getter
public abstract class CmExceptionBase extends RuntimeException {

    private final IErrorCode errorCode;

    private final String message;

    private final boolean retryable;

    private final HttpStatus httpStatus;

    private final ErrorType type;

    private final Map<String, Object> arguments;

    public CmExceptionBase(ErrorDetails errorDetails, HttpStatus httpStatus) {
        super(buildMessage(errorDetails.getCode(), errorDetails.getMessage(), errorDetails.isRetryable(), errorDetails.getType(), httpStatus));
        this.errorCode = errorDetails.getCode();
        this.message = errorDetails.getMessage();
        this.retryable = errorDetails.isRetryable();
        this.type = errorDetails.getType();
        this.httpStatus = httpStatus;
        this.arguments = Map.copyOf(errorDetails.getArguments());
    }

    public CmExceptionBase(ErrorDetails errorDetails, HttpStatus httpStatus, Throwable cause) {
        super(buildMessage(errorDetails.getCode(), errorDetails.getMessage(), errorDetails.isRetryable(), errorDetails.getType(), httpStatus), cause);
        this.errorCode = errorDetails.getCode();
        this.message = errorDetails.getMessage();
        this.retryable = errorDetails.isRetryable();
        this.type = errorDetails.getType();
        this.httpStatus = httpStatus;
        this.arguments = Map.copyOf(errorDetails.getArguments());
    }

    private static String buildMessage(IErrorCode errorCode, String message, boolean retryable, ErrorType type, HttpStatus httpStatus) {
        return String.format("errorCode=%s, message=%s, retryable=%s, type=%s, httpStatus=%s",
                errorCode == null ? null : errorCode.name(), message, retryable, type, httpStatus);
    }

    public boolean isError(String code) {
        return code.equals(getCodeName());
    }

    public boolean isError(Enum<?> code) {
        return isError(code.name());
    }

    public String getCodeName() {
        return errorCode == null ? null : errorCode.name();
    }
}
