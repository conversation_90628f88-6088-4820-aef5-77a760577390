package com.baupos.retailmanager.common.error;

import org.slf4j.helpers.MessageFormatter;

/**
 * Base interface for all <code>ErrorCode</code>s
 */
public interface IErrorCode {
    String getMessage();
    String name();

    default String getMessage(Object... details) {
        return MessageFormatter.arrayFormat(getMessage(), details).getMessage();
    }

    default ErrorDetails details(Object... messageArguments) {
        return new ErrorDetails(this, getMessage(messageArguments));
    }

}
