package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.error.ErrorResponse;
import com.baupos.retailmanager.common.error.ErrorType;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.MalformedJwtException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.stream.Collectors;

import static com.baupos.retailmanager.common.error.CmErrorCode.INTERNAL_SERVER_ERROR;
import static com.baupos.retailmanager.common.error.CmErrorCode.INVALID_ARGUMENTS;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class RestExceptionHandler {

    /**
     * ServletRequestBindingException says its unrecoverable and related to
     * request binding. Hence it fits with 400
     */
    // 400 - BAD_REQUEST
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @Order(value = 0)
    public ErrorResponse requestValidationFailHandler(MethodArgumentNotValidException ex) {

        BindingResult bindingResult = ex.getBindingResult();

        StringBuilder sb = new StringBuilder()
                .append("Invalid request for object: ")
                .append(bindingResult.getObjectName())
                .append(", fields ")
                .append(bindingResult.getFieldErrors()
                        .stream()
                        .map(error -> String.format("{ %s: %s } ", error.getField(), error.getRejectedValue()))
                        .collect(Collectors.joining("", "[ ", "]")));

        log.warn(sb.toString());
        return ErrorResponse.builder()
                .codeName(INVALID_ARGUMENTS.name())
                .message(sb.toString())
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();
    }

    /**
     * ServletRequestBindingException says its unrecoverable and related to
     * request binding. Hence it fits with 400
     */
    // 400 - BAD_REQUEST
    @ExceptionHandler({MethodArgumentTypeMismatchException.class,
            CmBadRequestException.class, ServletRequestBindingException.class, IllegalArgumentException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @Order(value = 1)
    public ErrorResponse badRequestHandler(Exception ex) {
        return buildErrorResponse(ex, false);
    }

    // 401-UNAUTHORIZED
    @ExceptionHandler({CmAuthenticationException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @Order(value = 2)
    public ErrorResponse unauthorizedHandler(CmAuthenticationException ex) {
        return buildErrorResponse(ex, false);
    }

    // 401-UNAUTHORIZED
    @ExceptionHandler({AccessDeniedException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @Order(value = 2)
    public ErrorResponse accessDeniedExceptionHandler(AccessDeniedException ex) {
        return buildErrorResponse(new CmAuthenticationException(CmErrorCode.ACCESS_DENIED.details(), ex), false);
    }

    // 401-UNAUTHORIZED
    @ExceptionHandler({AuthenticationException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @Order(value = 2)
    public ErrorResponse authenticationExceptionHandler(AuthenticationException ex) {
        return buildErrorResponse(new CmAuthenticationException(CmErrorCode.AUTHENTICATION_ERROR.details(), ex), false);
    }

    @ExceptionHandler({JwtException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @Order(value = 3)
    public ErrorResponse jwtException(JwtException ex) {
        if (ex instanceof ExpiredJwtException) {
            return buildErrorResponse(new CmAuthenticationException(CmErrorCode.JWT_EXPIRED.details(), ex), false);
        }
        if (ex instanceof MalformedJwtException) {
            return buildErrorResponse(new CmAuthenticationException(CmErrorCode.MALFORMED_JWT.details(), ex), false);
        }
        return buildErrorResponse(new CmAuthenticationException(CmErrorCode.INTERNAL_SERVER_ERROR.details(), ex), false);
    }

    // 404-NOT_FOUND
    @ExceptionHandler({CmNotFoundException.class})
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @Order(value = 3)
    public ErrorResponse notFoundHandler(Exception ex) {
        return buildErrorResponse(ex, false);
    }

    // 500 - INTERNAL_SERVER_ERROR
    @ExceptionHandler({CmInternalServerException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @Order(value = 4)
    public ErrorResponse internalServerErrorHandler(Exception ex) {
        return buildErrorResponse(ex, false);
    }


    /**
     * Everything else is 500/INTERNAL_SERVER_ERROR.
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @Order(value = Ordered.LOWEST_PRECEDENCE)
    public ErrorResponse defaultHandler(Exception ex) {
        return buildErrorResponse(ex, false);
    }

    private ErrorResponse buildErrorResponse(Exception ex, boolean defaultRetry) {
        ErrorResponse response = getCmErrorResponse(ex, defaultRetry);
        logException(ex, response);
        return response;
    }

    private ErrorResponse getCmErrorResponse(Exception ex, boolean defaultRetry) {
        if (ex instanceof CmExceptionBase) {
            var cmException = (CmExceptionBase) ex;

            return ErrorResponse.builder()
                    .codeName(cmException.getCodeName())
                    .message(cmException.getMessage())
                    .retryable(cmException.isRetryable())
                    .type(cmException.getType())
                    .httpStatus(cmException.getHttpStatus())
                    .build();
        } else {
            // Unexpected exception type, we do not want to echo its error message back to the caller
            // in case it contains XSS input or PII information
            return getDefaultUnexpectedExceptionResponse(ex, defaultRetry);
        }
    }

    private ErrorResponse getDefaultUnexpectedExceptionResponse(Exception ex, boolean defaultRetry) {
        return ErrorResponse.builder()
                .codeName(INTERNAL_SERVER_ERROR.name())
                .message("Unexpected server exception of type " + ex.getClass().getSimpleName())
                .retryable(defaultRetry)
                .type(ErrorType.ABNORMAL)
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();
    }

    private void logException(Exception ex, ErrorResponse response) {
        String logLine = String.format("ErrorResponse=%s, message=%s", response, ex.getMessage());
        if (ex instanceof CmExceptionBase) {
            var cmException = (CmExceptionBase) ex;
            switch (cmException.getType()) {
                case NORMAL:
                    log.info(logLine, ex);
                    break;
                case WARNING:
                    log.warn(logLine, ex);
                    break;
                default:
                    log.error(logLine, ex);
            }
        } else {
            log.error(logLine, ex);
        }
    }

}
