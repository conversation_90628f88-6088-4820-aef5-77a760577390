package com.baupos.retailmanager.common.error;

import lombok.*;

import java.util.LinkedHashMap;
import java.util.Map;

@RequiredArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class ErrorDetails {
    private final IErrorCode code;
    private final String message;
    @Builder.Default
    private Map<String, Object> arguments = new LinkedHashMap<>();
    @Builder.Default
    private ErrorType type = ErrorType.ABNORMAL;
    @Builder.Default
    private boolean retryable = false;
}