package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.ErrorDetails;
import org.springframework.http.HttpStatus;

public class CmAuthenticationException extends CmExceptionBase {
    private static final long serialVersionUID = 2699622328401081759L;

    public CmAuthenticationException(ErrorDetails errorDetails) {
        super(errorDetails, HttpStatus.UNAUTHORIZED);
    }

    public CmAuthenticationException(ErrorDetails errorDetails, Throwable cause) {
        super(errorDetails, HttpStatus.UNAUTHORIZED, cause);
    }
}

