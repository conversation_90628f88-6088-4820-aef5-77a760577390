package com.baupos.retailmanager.common.error;

import lombok.*;
import org.springframework.http.HttpStatus;

@Data
@ToString(of = {"codeName", "message", "type", "httpStatus", "retryable"})
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
public class ErrorResponse {

    private String codeName;
    private String message;
    private ErrorType type;
    private HttpStatus httpStatus;
    @Builder.Default
    private boolean retryable = false;

    public static ErrorResponse of(CmErrorCode errorCode, HttpStatus httpStatus) {
        return ErrorResponse.builder()
                .codeName(errorCode.name())
                .message(errorCode.getMessage())
                .retryable(errorCode.isRetryable())
                .type(errorCode.getType())
                .httpStatus(httpStatus)
                .build();
    }
}
