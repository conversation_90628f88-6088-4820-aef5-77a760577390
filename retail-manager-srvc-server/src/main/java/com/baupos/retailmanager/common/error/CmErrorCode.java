package com.baupos.retailmanager.common.error;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CmErrorCode implements IErrorCode {
    INVALID_ARGUMENTS("Invalid arguments provided", false, ErrorType.NORMAL),
    MISSING_ARGUMENTS("Missing arguments {}", false, ErrorType.NORMAL),
    INTERNAL_SERVER_ERROR("Internal server error", false, ErrorType.ABNORMAL),
    USER_ALREADY_EXISTS("User already exists for the provided username {}", false, ErrorType.NORMAL),
    EMPLOYEE_ALREADY_EXISTS("Employee already exists for the provided email {}", false, ErrorType.NORMAL),
    CUSTOMER_ALREADY_EXISTS("Customer already exists with {}", false, ErrorType.NORMAL),
    CUSTOMER_NOT_FOUND("Customer not found {}", false, ErrorType.NORMAL),
    BRANCH_ALREADY_EXISTS("Branch already exists with {}", false, ErrorType.NORMAL),
    BR<PERSON>CH_NOT_FOUND("Branch not found {}", false, ErrorType.NORMAL),
    BAD_CREDENTIALS("User login failed for username {}", false, ErrorType.NORMAL),
    OPERATION_NOT_SUPPORTED_FOR_EXISTING_USERS("Operation not supported for existing users", false, ErrorType.NORMAL),
    USER_NOT_FOUND("User not found {}", false, ErrorType.NORMAL),
    EMPLOYEE_NOT_FOUND("Employee not found {}", false, ErrorType.NORMAL),
    SUBSCRIPTION_NOT_FOUND("Subscription not found for tenant {}", false, ErrorType.ABNORMAL),
    SUBSCRIPTION_NOT_ACTIVE("Subscription should be active {}", false, ErrorType.NORMAL),
    RECURRING_PAYMENT_ALREADY_EXISTS("There is a matching recurring payment on the same subscription {}", false, ErrorType.ABNORMAL),
    JWT_EXPIRED("User authentication token expired", false, ErrorType.NORMAL),
    MALFORMED_JWT("Malformed authentication token", false, ErrorType.ABNORMAL),
    ACCESS_DENIED("Access is not allowed to the requested resource", false, ErrorType.NORMAL),
    AUTHENTICATION_ERROR("Authentication error", false, ErrorType.NORMAL),
    PAYMENT_GATEWAY_ERROR("Error received from payment gateway", false, ErrorType.ABNORMAL);

    private final String message;
    private final boolean retryable;
    private final ErrorType type;

    public ErrorDetails details(Object... messageArguments) {
        return ErrorDetails.builder()
                .code(this)
                .message(getMessage(messageArguments))
                .retryable(this.retryable)
                .type(this.type)
                .build();
    }

}
