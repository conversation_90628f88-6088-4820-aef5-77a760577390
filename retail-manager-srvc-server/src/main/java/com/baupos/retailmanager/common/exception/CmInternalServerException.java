package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.ErrorDetails;
import org.springframework.http.HttpStatus;

public class CmInternalServerException extends CmExceptionBase {

    private static final long serialVersionUID = -3675425066372056376L;

    public CmInternalServerException(ErrorDetails errorDetails) {
        super(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public CmInternalServerException(ErrorDetails errorDetails, Throwable cause) {
        super(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR, cause);
    }
}