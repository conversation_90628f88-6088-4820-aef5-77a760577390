package com.baupos.retailmanager.common.utils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;


@Component
@RequiredArgsConstructor
public class DateUtils {

    private final Clock clock;

    public OffsetDateTime getStartOfMonth() {
        LocalDate localDate = LocalDate.now(clock);
        localDate = localDate.withDayOfMonth(1);
        ZoneOffset offset = ZoneOffset.UTC;
        return localDate.atStartOfDay(offset).toOffsetDateTime();
    }

    public OffsetDateTime getStartOfNextMonth() {
        LocalDate localDate = LocalDate.now(clock);
        localDate = localDate.plusMonths(1).withDayOfMonth(1);
        ZoneOffset offset = ZoneOffset.UTC;
        return localDate.atStartOfDay(offset).toOffsetDateTime();
    }
}
