package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.ErrorDetails;
import org.springframework.http.HttpStatus;

public class CmNotFoundException extends CmExceptionBase {

    public CmNotFoundException(ErrorDetails errorDetails) {
        super(errorDetails, HttpStatus.NOT_FOUND);
    }

    public CmNotFoundException(ErrorDetails errorDetails, Throwable cause) {
        super(errorDetails, HttpStatus.NOT_FOUND, cause);
    }
}

