package com.baupos.retailmanager.common.config;

import com.baupos.retailmanager.RetailManagerServer;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Clock;
import java.time.ZoneOffset;

@Configuration
@ComponentScan(basePackageClasses = RetailManagerServer.class)
@EnableJpaRepositories(basePackageClasses = RetailManagerServer.class)
@EntityScan(basePackageClasses = RetailManagerServer.class)
@EnableAsync
@EnableScheduling
public class RetailManagerServerConfiguration {

    public static final ZoneOffset DEFAULT_CLOCK_OFFSET = ZoneOffset.UTC;

    @Bean
    public Clock clock() {
        return Clock.system(DEFAULT_CLOCK_OFFSET);
    }
}
