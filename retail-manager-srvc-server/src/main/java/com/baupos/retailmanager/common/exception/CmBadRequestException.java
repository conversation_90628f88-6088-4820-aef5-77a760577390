package com.baupos.retailmanager.common.exception;

import com.baupos.retailmanager.common.error.ErrorDetails;
import org.springframework.http.HttpStatus;

public class CmBadRequestException extends CmExceptionBase {

    public CmBadRequestException(ErrorDetails errorDetails) {
        super(errorDetails, HttpStatus.BAD_REQUEST);
    }

    public CmBadRequestException(ErrorDetails errorDetails, Throwable cause) {
        super(errorDetails, HttpStatus.BAD_REQUEST, cause);
    }
}

