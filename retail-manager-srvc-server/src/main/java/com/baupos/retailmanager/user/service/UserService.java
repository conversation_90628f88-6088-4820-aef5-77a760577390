package com.baupos.retailmanager.user.service;

import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.external.s3.service.S3Service;
import com.baupos.retailmanager.user.domain.*;
import com.baupos.retailmanager.user.dto.GrantedResourceDto;
import com.baupos.retailmanager.user.event.UserCreatedEvent;
import com.baupos.retailmanager.user.service.sto.CreateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.FindUsersRequestSto;
import com.baupos.retailmanager.user.service.sto.UpdateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final TransactionTaskRunner transactionTaskRunner;
    private final S3Service s3Service;

    @Transactional(propagation = Propagation.REQUIRED)
    public UserSto createUser(CreateUserRequestSto createUserRequest) {
        if (userRepository.findByUsername(createUserRequest.getUsername()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.USER_ALREADY_EXISTS.details(createUserRequest.getUsername()));
        }
        User newUser = userRepository.save(userMapper.map(createUserRequest));

        log.info("User created {}", newUser);
        applicationEventPublisher.publishEvent(UserCreatedEvent.builder()
                .id(newUser.getId())
                .username(newUser.getUsername())
                .createDate(newUser.getCreateDate())
                .build());

        return userMapper.map(newUser);
    }

    @Transactional(readOnly = true)
    public Page<UserSto> findPage(FindUsersRequestSto request) {
        Page<User> usersPage = userRepository.findFiltered(request.getUsernameFilter(),
                PageRequest.of(request.getPageNumber(), request.getPageSize(),
                        Sort.by(request.getSortDirection(), request.getSortBy())));

        return new PageImpl<>(usersPage.getContent().stream().map(userMapper::map).toList(),
                usersPage.getPageable(), usersPage.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Optional<UUID> findTenantId(UUID userId) {
        Optional<User> user = userRepository.findById(userId);
        return user.map(User::getTenantId);
    }

    @Transactional(readOnly = true)
    public Optional<UserSto> findById(UUID userId) {
        Optional<User> user = userRepository.findByIdAndDeleteDateIsNull(userId);
        return user.map(userMapper::map);
    }

    public UserSto getById(UUID userId) {
        return transactionTaskRunner.readOnlyAndGet(() -> userRepository.findByIdAndDeleteDateIsNull(userId)
                .map(userMapper::map)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.USER_NOT_FOUND.details(userId))));
    }

    @Transactional(readOnly = true)
    public Optional<UserSto> findByUsername(String username) {
        return userRepository.findByUsernameAndDeleteDateIsNull(username)
                .map(userMapper::map);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public UserSto update(UpdateUserRequestSto request) {
        User user = userRepository.findByIdAndDeleteDateIsNull(request.getId())
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.USER_NOT_FOUND.details(request.getId())));

        Optional.ofNullable(request.getUsername()).ifPresent(username -> user.setUsername(username));
        Optional.ofNullable(request.getPassword()).ifPresent(password -> user.setPassword(password));


        // Update privileges if provided
        Optional.ofNullable(request.getPrivileges()).ifPresent(requestedPrivileges -> {
            List<Privilege> existingPrivileges = user.getPrivileges().stream().map(UserPrivilege::getPrivilege).toList();

            // Add requested privileges if not already present
            requestedPrivileges.stream()
                    .filter(requestedPrivilege -> !existingPrivileges.contains(requestedPrivilege))
                    .forEach(newPrivilege -> user.addPrivilege(UserPrivilege.builder()
                            .privilege(newPrivilege)
                            .build()));

            // Remove existing privileges not included on the request
            List<UserPrivilege> existingPrivilegesCopy = List.copyOf(user.getPrivileges());

            existingPrivilegesCopy.stream()
                    .filter(existingPrivilege -> !request.getPrivileges().contains(existingPrivilege.getPrivilege()))
                    .forEach(existingPrivilegeToRemove -> user.removePrivilege(existingPrivilegeToRemove));
        });

        // Update granted resources if provided
        // Granted resources could be used to model the accounts that an employee has access to,
        // or similar resource-based access control requirements.
        Optional.ofNullable(request.getGrantedResources()).ifPresent(requestedGrantedResources -> {
            List<String> existingGrantedResources = user.getGrantedResources().stream().map(UserGrantedResource::toString).toList();

            // Add requested granted resources if not already present
            requestedGrantedResources.stream()
                    .filter(requestedGrantedResource -> !existingGrantedResources.contains(requestedGrantedResource.toString()))
                    .forEach(newGrantedResource -> user.addGrantedResource(UserGrantedResource.builder()
                            .type(newGrantedResource.getType())
                            .key(newGrantedResource.getKey())
                            .build()));

            // Remove existing granted resources not included on the request
            List<String> requestedGrantedResourceStrings = request.getGrantedResources().stream().map(GrantedResourceDto::toString).toList();

            List<UserGrantedResource> existingGrantedResourcesCopy = List.copyOf(user.getGrantedResources());
            existingGrantedResourcesCopy.stream()
                    .filter(existingGrantedResource -> !requestedGrantedResourceStrings.contains(existingGrantedResource.toString()))
                    .forEach(existingGrantedResourceToRemove -> user.removeGrantedResource(existingGrantedResourceToRemove));
        });

        User updatedUser = userRepository.save(user);
        return userMapper.map(updatedUser);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void deleteUser(UUID id) {
        userRepository.deleteById(id);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public UserSto updatePassword(UUID userId, String password) {
        User user = userRepository.getReferenceById(userId);
        user.setPassword(password);
        return userMapper.map(userRepository.save(user));
    }

    @Transactional(propagation = Propagation.NEVER)
    public UserSto uploadProfilePicture(UUID userId, MultipartFile file) {
        try {
            String key = s3Service.uploadProfileImage(userId, findTenantId(userId).get(), file);
            log.info("Uploaded profile picture for user {} to {}", userId, key);
            return transactionTaskRunner.readWriteAndGet(() -> {
                User user = userRepository.getReferenceById(userId);
                user.setProfilePictureKey(key);
                return userMapper.map(userRepository.save(user));
            });
        } catch (Exception e) {
            // Handle exception and continue since this is not critical
            log.error("Could not upload profile picture for user {}", userId, e);
            return this.getById(userId);
        }
    }
}
