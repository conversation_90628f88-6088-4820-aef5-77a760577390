package com.baupos.retailmanager.user.service.sto;

import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.dto.GrantedResourceDto;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
@ToString(of = {"id", "username", "privileges", "grantedResources"})
public class UpdateUserRequestSto {
    @NotNull
    private final UUID id;
    @Nullable
    private final String username;
    @Nullable
    private final String password;
    @Nullable
    private final List<Privilege> privileges;
    @Nullable
    private final List<GrantedResourceDto> grantedResources;
}
