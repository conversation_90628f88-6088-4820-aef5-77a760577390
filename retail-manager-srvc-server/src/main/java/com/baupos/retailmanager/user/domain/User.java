package com.baupos.retailmanager.user.domain;

import com.baupos.retailmanager.common.domain.DatedEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TenantId;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@ToString(of = {"id", "username", "role", "tenantId"})
@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Table(name="\"user\"") // Forces using quotes around table name since it is a reserved word
public class User extends DatedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false, unique = true)
    private String username;

    @Column(nullable = false)
    private String password;

    @TenantId
    @Column(nullable = false)
    private UUID tenantId;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private Role role;

    @Column
    private String profilePictureKey;

    @Builder.Default
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<UserPrivilege> privileges = new ArrayList<>();

    @Builder.Default
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<UserGrantedResource> grantedResources = new ArrayList<>();

    @Column
    private OffsetDateTime deleteDate;

    public void addPrivilege(UserPrivilege privilege) {
        privileges.add(privilege);
        privilege.setUser(this);
    }

    public void removePrivilege(UserPrivilege privilege) {
        privileges.remove(privilege);
        privilege.setUser(null);
    }

    public void addGrantedResource(UserGrantedResource grantedResource) {
        grantedResources.add(grantedResource);
        grantedResource.setUser(this);
    }

    public void removeGrantedResource(UserGrantedResource grantedResource) {
        grantedResources.remove(grantedResource);
        grantedResource.setUser(null);
    }
}
