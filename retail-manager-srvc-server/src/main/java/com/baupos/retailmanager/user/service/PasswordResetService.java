package com.baupos.retailmanager.user.service;

import com.baupos.retailmanager.common.email.EmailService;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.user.domain.PasswordResetToken;
import com.baupos.retailmanager.user.domain.PasswordResetTokenRepository;
import com.baupos.retailmanager.user.domain.User;
import com.baupos.retailmanager.user.service.sto.PasswordResetTokenSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Clock;
import java.time.OffsetDateTime;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PasswordResetService {

    private final long TOKEN_VALIDITY_THRESHOLD_HOURS = 2;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;
    private final TransactionTaskRunner transactionTaskRunner;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final EmailService emailService;
    private final Clock clock;

    @Transactional(propagation = Propagation.NEVER)
    public void createPasswordResetToken(String username) {
        UserSto user = userService.findByUsername(username)
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.USER_NOT_FOUND.details(username)));

        String rawToken = UUID.randomUUID().toString();
        String encodedToken = passwordEncoder.encode(rawToken);

        transactionTaskRunner.readWrite(() -> map(passwordResetTokenRepository.save(PasswordResetToken.builder()
                .token(encodedToken)
                .user(User.builder().id(user.getId()).build())
                .build())));

        log.debug(String.format("Generated password reset token %s", rawToken));
        // Send token via email outside of DB tx
        // emailService.sendPasswordResetToken(user.getUsername(), rawToken);
    }

    @Transactional(propagation = Propagation.NEVER)
    public PasswordResetTokenSto validatePasswordResetToken(String username, String inputRawToken) {
        return passwordResetTokenRepository.findTopByUserUsernameAndCreateDateAfterOrderByCreateDateDesc(username,
                        OffsetDateTime.now(clock).minusHours(TOKEN_VALIDITY_THRESHOLD_HOURS))
                .map(this::map)
                .filter(existingToken -> passwordEncoder.matches(inputRawToken, existingToken.getToken()))
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.INVALID_ARGUMENTS.details()));
    }

    private PasswordResetTokenSto map(PasswordResetToken entity) {
        return PasswordResetTokenSto.builder()
                .userId(entity.getUser().getId())
                .token(entity.getToken())
                .build();
    }
}
