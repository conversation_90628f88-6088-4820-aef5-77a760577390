package com.baupos.retailmanager.user.domain;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, UUID> {
    Optional<PasswordResetToken> findTopByUserUsernameAndCreateDateAfterOrderByCreateDateDesc(String userName,
                                                                              OffsetDateTime createDateAfter);
}
