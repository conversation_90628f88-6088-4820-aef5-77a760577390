package com.baupos.retailmanager.user.service.sto;

import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Builder
@ToString(of = {"id", "username", "tenantId", "role", "profilePictureKey", "privileges", "grantedResources", "createDate"})
@RequiredArgsConstructor
public class UserSto {
    private final UUID id;
    private final String username;
    private final String password;
    private final UUID tenantId;
    private final Role role;
    private final String profilePictureKey;
    @Builder.Default
    private final List<Privilege> privileges = new ArrayList<>();
    @Builder.Default
    private final List<GrantedResourceSto> grantedResources = new ArrayList<>();
    private final OffsetDateTime createDate;
}
