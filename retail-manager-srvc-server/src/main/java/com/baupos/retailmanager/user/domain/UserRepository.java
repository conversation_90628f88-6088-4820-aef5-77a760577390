package com.baupos.retailmanager.user.domain;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    Optional<User> findByIdAndDeleteDateIsNull(UUID id);

    Optional<User> findByUsernameAndDeleteDateIsNull(String username);

    Optional<User> findByUsername(String username);

    @Query("""
            select u from User u
            where u.username like %:usernameFilter% or :usernameFilter is null and u.deleteDate is null
            """)
    Page<User> findFiltered(@Param("usernameFilter") String usernameFilter, Pageable pageable);

    @Query("""
            update User u set u.deleteDate = cast(now() as java.time.OffsetDateTime) where u.id = :id and u.deleteDate is null
            """)
    @Modifying
    void deleteById(@Param("id") UUID id);

}
