package com.baupos.retailmanager.user.service.sto;

import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.*;

@Getter
@Builder
@ToString(of = {"username", "tenantId", "role", "privileges"})
@RequiredArgsConstructor
public class CreateUserRequestSto {
    private final String username;
    private final String password;
    private final UUID tenantId;
    private final Role role;
    @Builder.Default
    private final Set<Privilege> privileges = new HashSet<>();
}
