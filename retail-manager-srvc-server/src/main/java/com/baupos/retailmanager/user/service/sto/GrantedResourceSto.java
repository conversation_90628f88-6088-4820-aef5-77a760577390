package com.baupos.retailmanager.user.service.sto;

import com.baupos.retailmanager.user.domain.ResourceType;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@RequiredArgsConstructor(staticName = "of")
public class GrantedResourceSto {

    private final ResourceType type;
    private final String key;

    @Override
    public String toString() {
        return String.format("%s:%s", getType(), getKey());
    }
}