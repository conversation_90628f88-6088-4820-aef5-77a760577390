package com.baupos.retailmanager.user.service;

import com.baupos.retailmanager.user.domain.*;
import com.baupos.retailmanager.user.dto.GrantedResourceDto;
import com.baupos.retailmanager.user.dto.UserDto;
import com.baupos.retailmanager.user.service.sto.CreateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.GrantedResourceSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.EnumSet;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class UserMapper {

    @Value("${aws.s3.public-url}")
    private String publicUrlPrefix;

    @Transactional(propagation = Propagation.MANDATORY)
    public UserSto map(User userEntity) {
        return UserSto.builder()
                .id(userEntity.getId())
                .username(userEntity.getUsername())
                .password(userEntity.getPassword())
                .tenantId(userEntity.getTenantId())
                .role(userEntity.getRole())
                .profilePictureKey(userEntity.getProfilePictureKey())
                .privileges(mapPrivileges(userEntity))
                .grantedResources(mapGrantedResources(userEntity))
                .createDate(userEntity.getCreateDate())
                .build();
    }

    private List<Privilege> mapPrivileges(User userEntity) {
        if (userEntity.getRole() == Role.OWNER || userEntity.getRole() == Role.ADMIN) {
            return EnumSet.allOf(Privilege.class).stream().toList();
        }
        return userEntity.getPrivileges().stream().map(UserPrivilege::getPrivilege).toList();
    }

    private List<GrantedResourceSto> mapGrantedResources(User userEntity) {
        List<GrantedResourceSto> baseGrantedResources = List.of(
                GrantedResourceSto.of(ResourceType.USER, userEntity.getId().toString()),
                GrantedResourceSto.of(ResourceType.TENANT, userEntity.getTenantId().toString())
        );

        List<GrantedResourceSto> storedGrantedResources = userEntity.getGrantedResources()
                .stream()
                .map(gr -> GrantedResourceSto.of(gr.getType(), gr.getKey()))
                .toList();

        return ListUtils.union(baseGrantedResources, storedGrantedResources);
    }

    public User map(CreateUserRequestSto request) {
        User user = User.builder()
                .username(request.getUsername())
                .password(request.getPassword())
                .role(request.getRole())
                .tenantId(request.getTenantId())
                .build();

        request.getPrivileges().stream()
                .map(p -> UserPrivilege.builder()
                        .privilege(p)
                        .build())
                .forEach(userPrivilege -> user.addPrivilege(userPrivilege));

        return user;
    }

    public UserDto mapToDto(UserSto user) {
        return UserDto.builder()
                .id(user.getId())
                .username(user.getUsername())
                .role(user.getRole())
                .profilePictureUrl(Optional.ofNullable(user.getProfilePictureKey())
                        .map(key -> String.format("%s%s", publicUrlPrefix, key))
                        .orElse(null))
                .privileges(user.getPrivileges())
                .grantedResources(user.getGrantedResources().stream()
                        .map(gr -> GrantedResourceDto.builder()
                                .type(gr.getType())
                                .key(gr.getKey())
                                .build())
                        .toList())
                .createDate(user.getCreateDate())
                .build();
    }

}
