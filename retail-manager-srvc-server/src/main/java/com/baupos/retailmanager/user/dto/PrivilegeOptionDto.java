package com.baupos.retailmanager.user.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Builder
@Getter
@RequiredArgsConstructor
public class PrivilegeOptionDto {
    private final String group;
    private final String name;

    /**
     * Indicates whether the option can be selected/unselected by the authenticated user.
     */
    @Builder.Default
    private final boolean disabled = false;

    /**
     * Indicates whether the option should be selected by default.
     */
    @Builder.Default
    private final boolean selected = false;
}
