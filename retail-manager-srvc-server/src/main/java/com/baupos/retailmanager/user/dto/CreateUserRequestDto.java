package com.baupos.retailmanager.user.dto;

import com.baupos.retailmanager.user.domain.Privilege;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public class CreateUserRequestDto {
    @NotNull
    @Size(min = 4, max = 60)
    private final String username;
    @NotNull
    @Size(min = 4, max = 20)
    private final String password;
    @NotEmpty
    private final Set<Privilege> privileges;
}
