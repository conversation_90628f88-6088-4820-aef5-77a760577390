package com.baupos.retailmanager.user.dto;

import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class UserDto {
    private final UUID id;
    private final String username;
    private final Role role;
    private final String profilePictureUrl;
    private final List<Privilege> privileges;
    private final List<GrantedResourceDto> grantedResources;
    private final OffsetDateTime createDate;
}
