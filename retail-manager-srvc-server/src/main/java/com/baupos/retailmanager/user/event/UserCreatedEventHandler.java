package com.baupos.retailmanager.user.event;

import com.baupos.retailmanager.common.email.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserCreatedEventHandler {

    @Value("${app.name}")
    private String appName;

    private final EmailService emailService;

    @Async
    @EventListener
    public void sendNewUserNotification(UserCreatedEvent event) {
        log.info("Handling {}", event);
        emailService.sendEmail(event.getUsername(),
                String.format("Welcome to %s", appName),
                String.format("Hello %s, /n Thank you for registering at %s", event.getUsername(), appName)
        );
    }
}
