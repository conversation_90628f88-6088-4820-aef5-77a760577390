package com.baupos.retailmanager.user.domain;

import lombok.Getter;

@Getter
public enum Privilege {
    USER_CREATE("Users", true),
    USER_READ("Users", true),
    USER_UPDATE("Users", true),
    USER_DELETE("Users", true),
    EMPLOYEE_CREATE("Employees", true),
    EMPLOYEE_READ("Employees", true),
    EMPLOYEE_UPDATE("Employees", true),
    EMPLOYEE_DELETE("Employees", true),
    CUSTOMER_CREATE("Customers", true),
    CUSTOMER_READ("Customers", true),
    CUSTOMER_UPDATE("Customers", true),
    CUSTOMER_DELETE("Customers", true),
    BRANCH_CREATE("Branches", true),
    BRANCH_READ("Branches", true),
    BRANCH_UPDATE("Branches", true),
    BRANCH_DELETE("Branches", true),
    TENANT_MANAGEMENT("Tenant", false);

    private final String group;
    private final boolean selectable;

    Privilege(String group, boolean selectable) {
        this.group = group;
        this.selectable = selectable;
    }
}
