package com.baupos.retailmanager.user;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.dto.CreateUserRequestDto;
import com.baupos.retailmanager.user.dto.UpdateUserRequestDto;
import com.baupos.retailmanager.user.dto.UserDto;
import com.baupos.retailmanager.user.service.UserMapper;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.CreateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.FindUsersRequestSto;
import com.baupos.retailmanager.user.service.sto.UpdateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;
import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/users")
public class UserController {

    private final UserService userService;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationFacade authenticationFacade;

    @PostMapping("/")
    @PreAuthorize("@authorizationChecks.canCreateUser(#createUserRequest)")
    public UserDto createUser(@Valid @RequestPart("user") CreateUserRequestDto createUserRequest,
                              @RequestPart(value = "file", required = false) MultipartFile file) {
        UserSto newUser = userService.createUser(CreateUserRequestSto.builder()
                .username(createUserRequest.getUsername())
                .password(passwordEncoder.encode(createUserRequest.getPassword()))
                .tenantId(authenticationFacade.getAuthenticatedTenantId().get())
                .role(Role.EMPLOYEE)
                .privileges(createUserRequest.getPrivileges())
                .build());

        if (file != null && !file.isEmpty()) {
            newUser = userService.uploadProfilePicture(newUser.getId(), file);
        }

        return userMapper.mapToDto(newUser);
    }

    @GetMapping("/")
    @PreAuthorize("@authorizationChecks.canReadUsers()")
    public PageDto<UserDto> listUsers(
            @RequestParam(value = "pageNumber", required = false, defaultValue = "1") @Min(1) Integer pageNumber,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") @Min(1) @Max(100) Integer pageSize,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createDate") String sortBy,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(value = "filter.username", required = false) String usernameFilter
    ) {
        Page<UserSto> users = userService.findPage(FindUsersRequestSto.builder()
                .pageNumber(pageNumber - 1)
                .pageSize(pageSize)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .usernameFilter(usernameFilter)
                .build());
        return new PageDto<>(users, userMapper::mapToDto);
    }

    @GetMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canReadUser(#id)")
    public UserDto getUser(@PathVariable UUID id) {
        return userService.findById(id)
                .map(userMapper::mapToDto)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.USER_NOT_FOUND.details(id)));
    }

    @PatchMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canUpdateUser(#id, #updateUserRequest)")
    public UserDto updateUser(@PathVariable UUID id, @RequestBody UpdateUserRequestDto updateUserRequest) {
        return userMapper.mapToDto(userService.update(UpdateUserRequestSto.builder()
                .id(id)
                .username(updateUserRequest.getUsername())
                .password(Optional.ofNullable(updateUserRequest.getPassword())
                        .filter(StringUtils::isNotBlank)
                        .map(passwordEncoder::encode)
                        .orElse(null))
                .privileges(updateUserRequest.getPrivileges())
                .grantedResources(updateUserRequest.getGrantedResources())
                .build()));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canDeleteUser(#id)")
    public ResponseEntity deleteUser(@PathVariable UUID id) {
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

}
