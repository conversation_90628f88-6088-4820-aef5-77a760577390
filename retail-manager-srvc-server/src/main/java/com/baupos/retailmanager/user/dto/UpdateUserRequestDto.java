package com.baupos.retailmanager.user.dto;

import com.baupos.retailmanager.user.domain.Privilege;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Getter
@RequiredArgsConstructor
public class UpdateUserRequestDto {
    @Nullable
    @Size(min = 4, max = 60)
    private final String username;
    @Nullable
    @Size(min = 4, max = 20)
    private final String password;
    @Nullable
    private final List<Privilege> privileges;
    @Nullable
    private final List<GrantedResourceDto> grantedResources;
}
