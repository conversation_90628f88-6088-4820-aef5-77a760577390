package com.baupos.retailmanager.meta;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.dto.PrivilegeOptionDto;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * Provides API metadata for FE usage when needed, for example:
 * Enum definitions
 * Select options
 * Error codes
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/meta")
public class MetaController {

    private final UserService userService;
    private final AuthenticationFacade authenticationFacade;

    /**
     * Returns options for the privilege selection drop down for creating or updating users.
     * - The options are enabled/disabled for selection based on the privileges of the authenticated user.
     * - When userId is provided, the privilege options are marked as selected if the user already has those,
     *   this is used from the update-user screen.
     *
     * @param userId
     * @return
     */
    @GetMapping("/privilege-options")
    @PreAuthorize("@authorizationChecks.canReadUsers()")
    public List<PrivilegeOptionDto> privilegeOptions(@RequestParam(name = "userId") Optional<String> userId) {
        UUID authenticatedUserId = authenticationFacade.getAuthenticatedUserDetails().get().getUserId();
        List<Privilege> authenticatedUserPrivileges = userService.getById(authenticatedUserId).getPrivileges();

        List<Privilege> providedUserPrivileges = userId.map(UUID::fromString)
                .map(userService::getById)
                .map(UserSto::getPrivileges)
                .orElse(List.of());

        return EnumSet.allOf(Privilege.class)
                .stream()
                .filter(Privilege::isSelectable)
                .map(privilege -> PrivilegeOptionDto.builder()
                        .group(privilege.getGroup())
                        .name(privilege.name())
                        .disabled(!authenticatedUserPrivileges.contains(privilege)
                                // A user is not able to change his own privileges
                                || Objects.equals(userId.orElse(null), authenticatedUserId.toString()))
                        .selected(providedUserPrivileges.contains(privilege))
                        .build())
                .toList();
    }

}
