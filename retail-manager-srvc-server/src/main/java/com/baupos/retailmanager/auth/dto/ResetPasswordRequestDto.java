package com.baupos.retailmanager.auth.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class ResetPasswordRequestDto {
    @NotNull
    private final String username;
    @NotNull
    private final String token;
    @NotNull
    @Size(min = 4, max = 20)
    private final String password;
}
