package com.baupos.retailmanager.auth;

import com.baupos.retailmanager.auth.dto.*;
import com.baupos.retailmanager.auth.service.*;
import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmAuthenticationException;
import com.baupos.retailmanager.user.dto.UserDto;
import com.baupos.retailmanager.user.service.PasswordResetService;
import com.baupos.retailmanager.user.service.UserMapper;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.PasswordResetTokenSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/auth")
public class AuthController {

    private final AuthenticationCookieManager authenticationCookieManager;
    private final AuthenticationManager authenticationManager;
    private final AuthUserDetailsService authUserDetailsService;
    private final AuthenticationFacade authenticationFacade;
    private final UserService userService;
    private final PasswordResetService passwordResetService;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;
    @Value("${sslEnabled:false}")
    private boolean sslEnabled;

    @PostMapping("/login")
    public LoginResponseDto login(HttpServletRequest httpRequest, HttpServletResponse httpResponse,
                                  @Valid @RequestBody LoginRequestDto loginRequestDto) {
        try {
            AuthUserDetailsSto userDetails = authUserDetailsService.loadUserByUsername(loginRequestDto.getUsername());
            String accessToken = jwtUtil.generateToken(userDetails);
            authenticationManager.authenticate(new AccessTokenAuthentication(loginRequestDto.getUsername(),
                    loginRequestDto.getPassword(), accessToken));

            authenticationCookieManager.setAccessTokenCookie(httpResponse, sslEnabled, accessToken, jwtUtil.getTokenMaxAgeSeconds());
            return LoginResponseDto.builder()
                    .user(userMapper.mapToDto(userService.findByUsername(loginRequestDto.getUsername()).get()))
                    .accessToken(accessToken)
                    .build();
        } catch (AuthenticationException e) {
            throw new CmAuthenticationException(CmErrorCode.BAD_CREDENTIALS.details(loginRequestDto.getUsername()), e);
        }
    }

    @PostMapping("/logout")
    public void logout(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        authenticationCookieManager.removeCookies(httpResponse, sslEnabled);
    }

    @PostMapping("/password-reset-token")
    public ResponseEntity createPasswordResetToken(HttpServletRequest httpRequest, HttpServletResponse httpResponse,
                                                   @Valid @RequestBody CreatePasswordResetTokenRequestDto requestBody) {
        passwordResetService.createPasswordResetToken(requestBody.getUsername());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/reset-password")
    public UserDto resetPassword(@Valid @RequestBody ResetPasswordRequestDto requestBody) {
        PasswordResetTokenSto passwordResetToken = passwordResetService
                .validatePasswordResetToken(requestBody.getUsername(), requestBody.getToken());

        UserSto updatedUser = userService.
                updatePassword(passwordResetToken.getUserId(), passwordEncoder.encode(requestBody.getPassword()));

        return userMapper.mapToDto(updatedUser);
    }

    @GetMapping("/userInfo")
    @PreAuthorize("@authorizationChecks.isLoggedIn()")
    public UserInfoResponseDto userInfo(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        UserSto loggedInUser = userService.getById(authenticationFacade.getAuthenticatedUserDetails().get().getUserId());
        return UserInfoResponseDto.builder()
                .user(userMapper.mapToDto(loggedInUser))
                .build();
    }

}
