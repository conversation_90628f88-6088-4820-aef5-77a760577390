package com.baupos.retailmanager.auth.service;

import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthUserDetailsService implements UserDetailsService {

    private final UserService userService;

    @Override
    public AuthUserDetailsSto loadUserByUsername(String username) throws UsernameNotFoundException {
        return userService.findByUsername(username)
                .map(this::map)
                .orElseThrow(() -> new UsernameNotFoundException("Username not found: " + username));
    }

    private AuthUserDetailsSto map(UserSto user) {
        return AuthUserDetailsSto.builder()
                .userId(user.getId())
                .tenantId(user.getTenantId())
                .username(user.getUsername())
                .password(user.getPassword())
                .authorities(user.getPrivileges().stream().map(p -> new SimpleGrantedAuthority(p.name())).toList())
                .grantedResources(user.getGrantedResources())
                .build();
    }
}

