package com.baupos.retailmanager.auth.service;

import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class AuthenticationFacade {

    public Optional<Authentication> getAuthentication() {
        Optional<Authentication> authentication = Optional.ofNullable(SecurityContextHolder.getContext())
                .map(SecurityContext::getAuthentication);

        return authentication
                .filter(auth -> auth.getPrincipal() instanceof AuthUserDetailsSto);
    }

    public Optional<UUID> getAuthenticatedTenantId() {
        return this.getAuthentication()
                .map(authentication -> (AuthUserDetailsSto) authentication.getPrincipal())
                .map(AuthUserDetailsSto::getTenantId);
    }

    public Optional<AuthUserDetailsSto> getAuthenticatedUserDetails() {
        return this.getAuthentication()
                .map(authentication -> (AuthUserDetailsSto) authentication.getPrincipal());
    }

}