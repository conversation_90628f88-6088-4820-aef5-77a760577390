package com.baupos.retailmanager.auth.service.sto;

import com.baupos.retailmanager.user.service.sto.GrantedResourceSto;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;
import java.util.UUID;

@Builder
@Getter
@ToString(of = {"userId", "tenantId", "username", "authorities", "grantedResources"})
public class AuthUserDetailsSto implements UserDetails {
    private UUID userId;
    private UUID tenantId;
    private String username;
    private String password;
    private List<SimpleGrantedAuthority> authorities;
    private List<GrantedResourceSto> grantedResources;

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
