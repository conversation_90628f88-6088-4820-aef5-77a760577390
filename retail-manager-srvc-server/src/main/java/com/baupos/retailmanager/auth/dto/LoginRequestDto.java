package com.baupos.retailmanager.auth.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class LoginRequestDto {
    @NotNull
    @Email
    private final String username;
    @NotNull
    @Size.List({
            @Size(min = 6, message = "{validation.password.size.too_short}"),
            @Size(max = 20, message = "{validation.password.size.too_long}")
    })
    private final String password;
}
