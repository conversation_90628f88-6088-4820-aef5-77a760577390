package com.baupos.retailmanager.auth.service;

import lombok.Getter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

@Getter
public class AccessTokenAuthentication extends UsernamePasswordAuthenticationToken {
    private final String accessToken;

    public AccessTokenAuthentication(Object principal, Object credentials, String accessToken) {
        super(principal, credentials);
        this.accessToken = accessToken;
    }

    public AccessTokenAuthentication(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities, String accessToken) {
        super(principal, credentials, authorities);
        this.accessToken = accessToken;
    }
}
