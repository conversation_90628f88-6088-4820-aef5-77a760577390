package com.baupos.retailmanager.auth.service;


import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * Authentication cookie manager
 */
@Slf4j
@Component
public class AuthenticationCookieManager {
    public static final String ACCESS_TOKEN_COOKIE_NAME = "access_token";

    @Setter
    @Value("${app.environment}")
    private String environment;

    public void setAccessTokenCookie(HttpServletResponse httpResponse, boolean secure, String accessToken, int expiresInSeconds) {
        setCookie(httpResponse, secure, ACCESS_TOKEN_COOKIE_NAME, accessToken, expiresInSeconds);
    }

    public void removeCookies(HttpServletResponse httpResponse, boolean secure) {
        removeCookie(httpResponse, secure, ACCESS_TOKEN_COOKIE_NAME);
    }

    public Optional<String> getAccessTokenCookie(HttpServletRequest httpRequest) {
        return getCookie(httpRequest, ACCESS_TOKEN_COOKIE_NAME);
    }

    private void setCookie(HttpServletResponse httpResponse, boolean secure, String name, String value, int expiresInSeconds) {
        ResponseCookie cookie = ResponseCookie
                .from(name, value)
                .secure(secure)
                .httpOnly(true)
                .maxAge(expiresInSeconds)
                .path("/")
                .sameSite("Strict")
                .build();

        httpResponse.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }

    private void removeCookie(HttpServletResponse httpResponse, boolean secure, String name) {
        ResponseCookie cookie = ResponseCookie
                .from(name, StringUtils.EMPTY)
                .secure(secure)
                .httpOnly(true)
                .maxAge(0)
                // UI server might not be hosted on same domain as API,
                // However SameSite:None is only allowed over SSL https connections,
                // so lets use SameSite:Lax if that's not the case.
                .sameSite(secure ? "None" : "Lax")
                .path("/")
                .build();

        httpResponse.setHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }

    private Optional<String> getCookie(HttpServletRequest httpRequest, String name) {
        Cookie[] cookies = httpRequest.getCookies();
        if (cookies == null) {
            return Optional.empty();
        }
        return Stream.of(cookies)
                .filter(cookie -> name.equals(cookie.getName()))
                .map(Cookie::getValue)
                .findFirst();
    }
}
