package com.baupos.retailmanager.customer.dto;

import com.baupos.retailmanager.customer.domain.Gender;
import jakarta.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString(of = {"firstName", "lastName"})
@RequiredArgsConstructor
public class UpdateCustomerRequestDto {
    @Nullable
    private final String firstName;
    @Nullable
    private final String lastName;
    @Nullable
    private final String email;
    @Nullable
    private final String phoneNumber;
    @Nullable
    private final String personalId;
    @Nullable
    private final String taxPayerId;
    @Nullable
    private final Gender gender;
    @Nullable
    private final String city;
    @Nullable
    private final String address;
}