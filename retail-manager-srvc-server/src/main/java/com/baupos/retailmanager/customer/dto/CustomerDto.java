package com.baupos.retailmanager.customer.dto;

import com.baupos.retailmanager.customer.domain.Gender;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class CustomerDto {
    private final UUID id;
    private final String firstName;
    private final String lastName;
    private final String email;
    private final String phoneNumber;
    private final String personalId;
    private final String taxPayerId;
    private final Gender gender;
    private final String city;
    private final String address;
}