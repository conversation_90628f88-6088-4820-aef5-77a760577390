package com.baupos.retailmanager.customer.dto;

import com.baupos.retailmanager.customer.domain.Gender;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString(of = {"firstName", "lastName"})
@RequiredArgsConstructor
public class CreateCustomerRequestDto {
    @NotNull
    private final String firstName;
    @NotNull
    private final String lastName;
    private final String email;
    private final String phoneNumber;
    private final String personalId;
    private final String taxPayerId;
    private final Gender gender;
    private final String city;
    private final String address;
}