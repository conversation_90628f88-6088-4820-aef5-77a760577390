package com.baupos.retailmanager.customer.service;

import com.baupos.retailmanager.customer.domain.Customer;
import com.baupos.retailmanager.customer.dto.CustomerDto;
import com.baupos.retailmanager.customer.service.sto.CustomerSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class CustomerMapper {

    @Transactional(propagation = Propagation.MANDATORY)
    public CustomerSto map(Customer customerEntity) {
        return CustomerSto.builder()
                .id(customerEntity.getId())
                .firstName(customerEntity.getFirstName())
                .lastName(customerEntity.getLastName())
                .email(customerEntity.getEmail())
                .phoneNumber(customerEntity.getPhoneNumber())
                .personalId(customerEntity.getPersonalId())
                .taxPayerId(customerEntity.getTaxPayerId())
                .gender(customerEntity.getGender())
                .city(customerEntity.getCity())
                .address(customerEntity.getAddress())
                .tenantId(customerEntity.getTenantId())
                .build();
    }

    public CustomerDto mapToDto(CustomerSto customerSto) {
        return CustomerDto.builder()
                .id(customerSto.getId())
                .firstName(customerSto.getFirstName())
                .lastName(customerSto.getLastName())
                .email(customerSto.getEmail())
                .phoneNumber(customerSto.getPhoneNumber())
                .personalId(customerSto.getPersonalId())
                .taxPayerId(customerSto.getTaxPayerId())
                .gender(customerSto.getGender())
                .city(customerSto.getCity())
                .address(customerSto.getAddress())
                .build();
    }
}