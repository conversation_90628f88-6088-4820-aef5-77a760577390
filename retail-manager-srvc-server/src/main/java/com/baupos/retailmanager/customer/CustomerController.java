package com.baupos.retailmanager.customer;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import com.baupos.retailmanager.customer.dto.CreateCustomerRequestDto;
import com.baupos.retailmanager.customer.dto.CustomerDto;
import com.baupos.retailmanager.customer.dto.UpdateCustomerRequestDto;
import com.baupos.retailmanager.customer.service.CustomerMapper;
import com.baupos.retailmanager.customer.service.CustomerService;
import com.baupos.retailmanager.customer.service.sto.CreateCustomerRequestSto;
import com.baupos.retailmanager.customer.service.sto.CustomerSto;
import com.baupos.retailmanager.customer.service.sto.FindCustomersRequestSto;
import com.baupos.retailmanager.customer.service.sto.UpdateCustomerRequestSto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/customers")
public class CustomerController {

    private final CustomerService customerService;
    private final CustomerMapper customerMapper;
    private final AuthenticationFacade authenticationFacade;

    @PostMapping("/")
    @PreAuthorize("@authorizationChecks.canCreateCustomer(#createCustomerRequest)")
    public CustomerDto createCustomer(@Valid @RequestBody CreateCustomerRequestDto createCustomerRequest) {
        CustomerSto newCustomer = customerService.createCustomer(CreateCustomerRequestSto.builder()
                .firstName(createCustomerRequest.getFirstName())
                .lastName(createCustomerRequest.getLastName())
                .email(createCustomerRequest.getEmail())
                .phoneNumber(createCustomerRequest.getPhoneNumber())
                .personalId(createCustomerRequest.getPersonalId())
                .taxPayerId(createCustomerRequest.getTaxPayerId())
                .gender(createCustomerRequest.getGender())
                .city(createCustomerRequest.getCity())
                .address(createCustomerRequest.getAddress())
                .tenantId(authenticationFacade.getAuthenticatedTenantId().get())
                .build());
        return customerMapper.mapToDto(newCustomer);
    }

    @GetMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canReadCustomer(#id)")
    public CustomerDto getCustomer(@PathVariable UUID id) {
        return customerMapper.mapToDto(customerService.findById(id)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.CUSTOMER_NOT_FOUND.details(id))));
    }

    @GetMapping("/")
    @PreAuthorize("@authorizationChecks.canReadCustomers()")
    public PageDto<CustomerDto> listCustomers(
            @RequestParam(value = "pageNumber", required = false, defaultValue = "1") @Min(1) Integer pageNumber,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") @Min(1) @Max(100) Integer pageSize,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createDate") String sortBy,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(value = "filter.firstName", required = false) String firstNameFilter
    ) {
        Page<CustomerSto> customers = customerService.findPage(FindCustomersRequestSto.builder()
                .pageNumber(pageNumber - 1)
                .pageSize(pageSize)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .firstNameFilter(firstNameFilter)
                .build());
        return new PageDto<>(customers, customerMapper::mapToDto);
    }

    @PutMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canUpdateCustomer(#id, #updateCustomerRequest)")
    public CustomerDto updateCustomer(@PathVariable UUID id, @RequestBody UpdateCustomerRequestDto updateCustomerRequest) {
        return customerMapper.mapToDto(customerService.update(UpdateCustomerRequestSto.builder()
                .id(id)
                .firstName(updateCustomerRequest.getFirstName())
                .lastName(updateCustomerRequest.getLastName())
                .email(updateCustomerRequest.getEmail())
                .phoneNumber(updateCustomerRequest.getPhoneNumber())
                .personalId(updateCustomerRequest.getPersonalId())
                .taxPayerId(updateCustomerRequest.getTaxPayerId())
                .gender(updateCustomerRequest.getGender())
                .city(updateCustomerRequest.getCity())
                .address(updateCustomerRequest.getAddress())
                .build()));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canDeleteCustomer(#id)")
    public ResponseEntity deleteCustomer(@PathVariable UUID id) {
        customerService.deleteCustomer(id);
        return ResponseEntity.ok().build();
    }

}
