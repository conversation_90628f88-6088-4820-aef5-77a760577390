package com.baupos.retailmanager.customer.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;

@Getter
@Builder
@RequiredArgsConstructor
public class FindCustomersRequestSto {
    private final Integer pageNumber;
    private final Integer pageSize;
    private final String sortBy;
    private final Sort.Direction sortDirection;
    private final String firstNameFilter;
}