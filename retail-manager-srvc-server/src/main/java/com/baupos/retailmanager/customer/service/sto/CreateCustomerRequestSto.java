package com.baupos.retailmanager.customer.service.sto;

import com.baupos.retailmanager.customer.domain.Gender;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.UUID;

@Getter
@Builder
@ToString(of = {"firstName", "tenantId"})
@RequiredArgsConstructor
public class CreateCustomerRequestSto {
    private final String firstName;
    private final String lastName;
    private final String email;
    private final String phoneNumber;
    private final String personalId;
    private final String taxPayerId;
    private final Gender gender;
    private final String city;
    private final String address;
    private final UUID tenantId;
}