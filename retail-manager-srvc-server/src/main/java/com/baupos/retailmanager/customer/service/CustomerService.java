package com.baupos.retailmanager.customer.service;

import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.customer.domain.Customer;
import com.baupos.retailmanager.customer.domain.CustomerRepository;
import com.baupos.retailmanager.customer.service.sto.CreateCustomerRequestSto;
import com.baupos.retailmanager.customer.service.sto.CustomerSto;
import com.baupos.retailmanager.customer.service.sto.FindCustomersRequestSto;
import com.baupos.retailmanager.customer.service.sto.UpdateCustomerRequestSto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@RequiredArgsConstructor
public class CustomerService {

    private final CustomerRepository customerRepository;
    private final CustomerMapper customerMapper;

    @Transactional(propagation = Propagation.REQUIRED)
    public CustomerSto createCustomer(CreateCustomerRequestSto createCustomerRequest) {
        if (!isBlank(createCustomerRequest.getEmail())
                && customerRepository.findByEmail(createCustomerRequest.getEmail()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.CUSTOMER_ALREADY_EXISTS
                    .details(String.format("email=%s", createCustomerRequest.getEmail())));
        }

        if (!isBlank(createCustomerRequest.getPersonalId())
                && customerRepository.findByPersonalId(createCustomerRequest.getEmail()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.CUSTOMER_ALREADY_EXISTS
                    .details(String.format("personalId=%s", createCustomerRequest.getPersonalId())));
        }

        Customer newCustomer = customerRepository.save(Customer.builder()
                .firstName(createCustomerRequest.getFirstName())
                .lastName(createCustomerRequest.getLastName())
                .email(createCustomerRequest.getEmail())
                .phoneNumber(createCustomerRequest.getPhoneNumber())
                .personalId(createCustomerRequest.getPersonalId())
                .taxPayerId(createCustomerRequest.getTaxPayerId())
                .gender(createCustomerRequest.getGender())
                .city(createCustomerRequest.getCity())
                .address(createCustomerRequest.getAddress())
                .tenantId(createCustomerRequest.getTenantId())
                .build());

        return customerMapper.map(newCustomer);
    }

    @Transactional(readOnly = true)
    public Optional<CustomerSto> findById(UUID customerId) {
        return customerRepository.findByIdAndDeleteDateIsNull(customerId)
                .map(customerMapper::map);
    }

    @Transactional(readOnly = true)
    public Page<CustomerSto> findPage(FindCustomersRequestSto request) {
        Page<Customer> customerPage = customerRepository.findFiltered(request.getFirstNameFilter(),
                PageRequest.of(request.getPageNumber(), request.getPageSize(),
                        Sort.by(request.getSortDirection(), request.getSortBy())));

        return new PageImpl<>(customerPage.getContent().stream().map(customerMapper::map).toList(),
                customerPage.getPageable(), customerPage.getTotalElements());
    }

    @Transactional
    public CustomerSto update(UpdateCustomerRequestSto request) {
        Customer customer = customerRepository.findByIdAndDeleteDateIsNull(request.getId())
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.CUSTOMER_NOT_FOUND.details(request.getId())));

        customer.setFirstName(request.getFirstName());
        customer.setLastName(request.getLastName());
        customer.setEmail(request.getEmail());
        customer.setPhoneNumber(request.getPhoneNumber());
        customer.setPersonalId(request.getPersonalId());
        customer.setTaxPayerId(request.getTaxPayerId());
        customer.setGender(request.getGender());
        customer.setCity(request.getCity());
        customer.setAddress(request.getAddress());

        Customer updatedCustomer = customerRepository.save(customer);
        return customerMapper.map(updatedCustomer);
    }

    @Transactional(readOnly = true)
    public Optional<UUID> findTenantId(UUID customerId) {
        return customerRepository.findById(customerId).map(Customer::getTenantId);
    }

    @Transactional
    public void deleteCustomer(UUID id) {
        customerRepository.deleteById(id);
    }

}
