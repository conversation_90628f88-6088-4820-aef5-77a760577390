package com.baupos.retailmanager.customer.domain;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, UUID> {

    Optional<Customer> findByEmail(String email);

    Optional<Customer> findByPersonalId(String personalId);

    @Query("""
            select c from Customer c
            where c.firstName like %:firstNameFilter% or :firstNameFilter is null and c.deleteDate is null
            """)
    Page<Customer> findFiltered(@Param("firstNameFilter") String firstNameFilter, Pageable pageable);

    Optional<Customer> findByIdAndDeleteDateIsNull(UUID id);

    @Query("""
            update Customer c set c.deleteDate = cast(now() as java.time.OffsetDateTime) where c.id = :id and c.deleteDate is null
            """)
    @Modifying
    void deleteById(@Param("id") UUID id);
}
