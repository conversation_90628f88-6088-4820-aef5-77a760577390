package com.baupos.retailmanager.tenant.domain;

import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TenantInvoiceRepository extends JpaRepository<TenantInvoice, UUID> {

    @Query("""
           SELECT ti FROM TenantInvoice ti
           JOIN ti.items i
           WHERE ti.subscription.id = :subscriptionId
           AND ti.status = :status
           AND ti.issueDate > :issueDateAfter
           GROUP BY ti
           HAVING SUM(i.price * i.quantity) = :invoiceTotal
           """)
    Optional<TenantInvoice> findBySubscriptionIdAndStatusAndIssueDateAfterAndInvoiceTotal(
            @Param("subscriptionId") UUID subscriptionId,
            @Param("status") TenantInvoiceStatus status,
            @Param("issueDateAfter") OffsetDateTime issueDateAfter,
            @Param("invoiceTotal") BigDecimal invoiceTotal);

}
