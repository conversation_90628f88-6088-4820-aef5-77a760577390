package com.baupos.retailmanager.tenant.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class TenantSettingsDto {
    private final String companyName;
    private final String locale;
    private final String timezone;
    private final String emailAddress;
    private final String phoneNumber;
    private final String country;
    private final String city;
    private final String address;
}
