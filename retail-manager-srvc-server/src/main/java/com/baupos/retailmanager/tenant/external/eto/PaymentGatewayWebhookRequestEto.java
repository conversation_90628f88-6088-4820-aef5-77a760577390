package com.baupos.retailmanager.tenant.external.eto;


import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.OffsetDateTime;

/**
 * Model for webhooks received from the PaymentGateway (MercadoPago)
 */
@Getter
@Builder
@ToString
public class PaymentGatewayWebhookRequestEto {
    /**
     * Seller identifier
     */
    private Long userId;
    private String apiVersion;
    /**
     * Either:
     * {payment, subscription_authorized_payment, subscription_preapproval}
     * ref: https://www.mercadopago.com.ar/developers/es/docs/your-integrations/notifications/webhooks
     */
    private String type;
    private String action;
    private Data data;
    private OffsetDateTime dateCreated;


    @Getter
    public static class Data {
        private Long id;
    }
}

