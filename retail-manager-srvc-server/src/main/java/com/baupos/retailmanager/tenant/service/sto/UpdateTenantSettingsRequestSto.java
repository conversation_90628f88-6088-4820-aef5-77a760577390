package com.baupos.retailmanager.tenant.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.UUID;

@Getter
@Builder
@ToString(of = {"tenantId", "companyName"})
@RequiredArgsConstructor
public class UpdateTenantSettingsRequestSto {
    private final UUID tenantId;
    private final String companyName;
    private final String locale;
    private final String timezone;
    private final String emailAddress;
    private final String phoneNumber;
    private final String country;
    private final String city;
    private final String address;
}
