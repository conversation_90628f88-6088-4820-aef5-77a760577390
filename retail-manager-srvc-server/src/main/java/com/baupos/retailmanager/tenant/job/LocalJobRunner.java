package com.baupos.retailmanager.tenant.job;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LocalJobRunner {

    private final TenantInvoiceGenerationJob tenantInvoiceGenerationJob;
    private final TenantOverdueSubscriptionSuspensionJob tenantOverdueSubscriptionSuspensionJob;
    private final TenantTrialSubscriptionTerminationJob tenantTrialSubscriptionTerminationJob;

    //@EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() {
        // just for testing
        tenantTrialSubscriptionTerminationJob.terminateExpiredTrialSubscriptions();
        tenantInvoiceGenerationJob.generateMonthlyInvoicesForActiveSubscriptions();

    }
}
