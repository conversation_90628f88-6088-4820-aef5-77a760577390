package com.baupos.retailmanager.tenant.domain;

import com.baupos.retailmanager.common.domain.DatedEntity;
import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TenantId;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class TenantInvoice extends DatedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @TenantId
    @Column(nullable = false)
    private UUID tenantId;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TenantInvoiceStatus status;

    @Column(nullable = false)
    private OffsetDateTime issueDate;

    @Column(nullable = false)
    private OffsetDateTime dueDate;

    @Builder.Default
    @OneToMany(mappedBy = "invoice", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<TenantInvoiceItem> items = new ArrayList<>();

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "tenant_subscription_id", nullable = false)
    private TenantSubscription subscription;

    @Builder.Default
    @OneToMany(mappedBy = "invoice", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<TenantInvoicePayment> payments = new ArrayList<>();

    public void addItem(TenantInvoiceItem item) {
        items.add(item);
        item.setInvoice(this);
    }

    public void removeItem(TenantInvoiceItem item) {
        items.remove(item);
        item.setInvoice(null);
    }

    public BigDecimal getInvoiceTotal() {
        return items.stream().map(TenantInvoiceItem::getItemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
