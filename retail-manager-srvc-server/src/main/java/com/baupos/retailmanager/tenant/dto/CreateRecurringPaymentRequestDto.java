package com.baupos.retailmanager.tenant.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;

@Getter
@Builder
@RequiredArgsConstructor
public class CreateRecurringPaymentRequestDto {
    @NotNull
    private final String subscriptionId;
    @NotNull
    private final BigDecimal amount;
    @NotNull
    private final String cardToken;
    @NotNull
    private final String payerEmail;
}
