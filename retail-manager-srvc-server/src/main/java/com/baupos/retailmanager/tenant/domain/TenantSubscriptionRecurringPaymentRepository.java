package com.baupos.retailmanager.tenant.domain;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TenantSubscriptionRecurringPaymentRepository extends JpaRepository<TenantSubscriptionRecurringPayment, UUID> {

    Optional<TenantSubscriptionRecurringPayment> findByExternalId(String externalId);

    List<TenantSubscriptionRecurringPayment> findBySubscriptionIdAndAmount(UUID subscriptionId, BigDecimal amount);
}
