package com.baupos.retailmanager.tenant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionCode;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class TenantSubscriptionDto {
    private final UUID id;
    private final TenantSubscriptionCode code;
    private final TenantSubscriptionStatus status;
    private final OffsetDateTime createDate;
    private final List<TenantInvoiceDto> invoices;

    @JsonProperty
    public OffsetDateTime getNextDueDate() {
        return invoices.stream()
                .map(TenantInvoiceDto::getDueDate)
                .max(OffsetDateTime::compareTo)
                .orElse(null);
    }

    @JsonProperty
    public OffsetDateTime getTrialExpirationDate() {
        // TODO: find out best way to use ${subscription.trialDuration}
        return createDate.plusDays(15);
    }
}
