package com.baupos.retailmanager.tenant.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Getter
@Setter
@Builder
public class TenantInvoiceItemSto {
    private String itemName;
    private OffsetDateTime from;
    private OffsetDateTime to;
    private BigDecimal quantity;
    private BigDecimal price;

    public BigDecimal getItemTotal() {
        return price.multiply(quantity);
    }
}
