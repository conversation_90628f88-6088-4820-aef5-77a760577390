package com.baupos.retailmanager.tenant.external;

import com.baupos.retailmanager.tenant.external.eto.PaymentGatewayWebhookRequestEto;
import com.baupos.retailmanager.tenant.external.service.PaymentGatewayWebhookHandlerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/payment-gateway/webhook")
public class PaymentGatewayWebhookHandlerController {

    private final PaymentGatewayWebhookHandlerService paymentGatewayWebhookHandlerService;

    @PostMapping("/process")
    public ResponseEntity<Void> processWebhook(@RequestBody PaymentGatewayWebhookRequestEto payload) {
        paymentGatewayWebhookHandlerService.handleWebhook(payload);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

}
