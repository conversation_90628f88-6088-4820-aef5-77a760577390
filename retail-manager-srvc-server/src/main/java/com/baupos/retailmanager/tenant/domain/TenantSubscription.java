package com.baupos.retailmanager.tenant.domain;

import com.baupos.retailmanager.common.domain.DatedEntity;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionCode;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TenantId;

import java.time.OffsetDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class TenantSubscription extends DatedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @TenantId
    @Column(nullable = false)
    private UUID tenantId;

    @Column(nullable = false)
    private TenantSubscriptionCode code;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TenantSubscriptionStatus status;

    @Column
    private OffsetDateTime activationDate;

    @Builder.Default
    @OneToMany(mappedBy = "subscription", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("dueDate")
    private List<TenantInvoice> invoices = new LinkedList<>();
}
