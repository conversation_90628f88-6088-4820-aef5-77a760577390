package com.baupos.retailmanager.tenant.external.eto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Getter
@Builder
@RequiredArgsConstructor
public class MpCreatePreapprovalRequestEto {
    private final String payerEmail;
    private final String cardTokenId;
    private final String externalReference;
    private final String reason;
    private final MpAutoRecurringDetails autoRecurring;
    @Builder.Default
    private final String status = "authorized";

    @Getter
    @Builder
    @RequiredArgsConstructor
    public static class MpAutoRecurringDetails {
        @Builder.Default
        private final String currencyId = "ARS";
        private final BigDecimal transactionAmount;
        @Builder.Default
        private final Integer frequency = 1;
        @Builder.Default
        private final String frequencyType = "months";
        private final OffsetDateTime startDate;
        private final OffsetDateTime endDate;
        private final Integer billingDay;
        private final Boolean billingDayProportional;
    }
}
