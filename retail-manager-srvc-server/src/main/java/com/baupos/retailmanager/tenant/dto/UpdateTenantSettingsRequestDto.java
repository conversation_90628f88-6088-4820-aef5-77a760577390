package com.baupos.retailmanager.tenant.dto;

import jakarta.validation.constraints.Email;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Builder
@Getter
@RequiredArgsConstructor
public class UpdateTenantSettingsRequestDto {
    private final String companyName;
    private final String locale;
    private final String timezone;
    @Email
    private final String emailAddress;
    private final String phoneNumber;
    private final String country;
    private final String city;
    private final String address;
}
