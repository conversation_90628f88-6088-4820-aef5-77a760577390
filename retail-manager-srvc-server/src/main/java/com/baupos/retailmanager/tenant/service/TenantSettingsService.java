package com.baupos.retailmanager.tenant.service;

import com.baupos.retailmanager.tenant.domain.TenantSettings;
import com.baupos.retailmanager.tenant.domain.TenantSettingsRepository;
import com.baupos.retailmanager.tenant.service.sto.TenantSettingsSto;
import com.baupos.retailmanager.tenant.service.sto.UpdateTenantSettingsRequestSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class TenantSettingsService {

    private final TenantSettingsRepository tenantSettingsRepository;
    private final TenantSettingsMapper tenantSettingsMapper;

    @Transactional(readOnly = true)
    public Optional<TenantSettingsSto> findByTenantId(UUID tenantId) {
        return tenantSettingsRepository.findByTenantId(tenantId)
                .map(tenantSettingsMapper::map);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public TenantSettingsSto updateTenantSettings(UpdateTenantSettingsRequestSto request) {
        TenantSettings tenantSettings = tenantSettingsRepository.findByTenantId(request.getTenantId())
                .orElseGet(() -> tenantSettingsRepository.save(TenantSettings.builder()
                        .tenantId(request.getTenantId())
                        .build()));

        // Update all fields
        tenantSettings.setCompanyName(request.getCompanyName());
        tenantSettings.setLocale(request.getLocale());
        tenantSettings.setTimezone(request.getTimezone());
        tenantSettings.setEmailAddress(request.getEmailAddress());
        tenantSettings.setPhoneNumber(request.getPhoneNumber());
        tenantSettings.setCountry(request.getCountry());
        tenantSettings.setCity(request.getCity());
        tenantSettings.setAddress(request.getAddress());

        TenantSettings savedSettings = tenantSettingsRepository.save(tenantSettings);
        return tenantSettingsMapper.map(savedSettings);
    }
}
