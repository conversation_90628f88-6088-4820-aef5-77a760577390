package com.baupos.retailmanager.tenant.external.config;

import com.mercadopago.MercadoPagoConfig;
import com.mercadopago.client.payment.PaymentClient;
import com.mercadopago.client.preapproval.PreapprovalClient;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
@RequiredArgsConstructor
public class PaymentGatewayConfiguration {

    @Value("${mercadopago.accessToken}")
    private String mercadoPagoAccessToken;
    @Value("${mercadopago.baseUrl}")
    private String mercadoPagoBaseUrl;

    /**
     * Sets config for working with MercadoPago SDK client, but some request objects are outdated,
     * so we use our own {@link #mercadoPagoClient()) in some cases.
     */
    @PostConstruct
    public void initMercadoPagoSdk() {
        MercadoPagoConfig.setAccessToken(mercadoPagoAccessToken);
    }

    @Bean
    public WebClient mercadoPagoClient() {
        return WebClient.builder()
                .baseUrl(mercadoPagoBaseUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", mercadoPagoAccessToken))
                .build();
    }

    @Bean
    public PaymentClient paymentClient() {
        return new PaymentClient();
    }

    @Bean
    public PreapprovalClient preapprovalClient() {
        return new PreapprovalClient();
    }

}
