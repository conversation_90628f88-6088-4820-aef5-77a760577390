package com.baupos.retailmanager.tenant.service.sto;

import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionCode;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class TenantSubscriptionSto {
    private final UUID id;
    private final UUID tenantId;
    private final TenantSubscriptionCode code;
    private final TenantSubscriptionStatus status;
    private final OffsetDateTime activationDate;
    private final OffsetDateTime createDate;
    private final List<TenantInvoiceSto> invoices;
}
