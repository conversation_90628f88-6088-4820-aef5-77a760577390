package com.baupos.retailmanager.tenant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Builder
@Getter
public class TenantInvoiceDto {

    private UUID id;
    private UUID tenantId;
    private TenantInvoiceStatus status;
    private OffsetDateTime issueDate;
    private OffsetDateTime dueDate;
    @Builder.Default
    private List<TenantInvoiceItemDto> items = new ArrayList<>();


    @JsonProperty
    public BigDecimal getInvoiceTotal() {
        return items.stream().map(TenantInvoiceItemDto::getItemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
