## Tenant domain
A tenant represents a client.

Multiple users can be created under the same tenant.
Every entity is associated to a single tenant, the tenantId from the authenticate user is used to scope down 
the entities the user has access to. This is done via Hibernate multi-tenancy via discriminator column: 
https://docs.jboss.org/hibernate/orm/6.1/userguide/html_single/Hibernate_User_Guide.html#multitenacy-discriminator

Tenants are allowed to use the system by keeping an active subscription.

Subscriptions generate monthly invoices via a job, 
for a subscription to remain active no overdue/expired invoices should exist.

Invoice payments affect invoice status and thus subscription status.

Invoice payments are debited automatically when there is a recurring payment 
configured at the payments gateway (MercadoPago), and payment records are ingested via webhook handlers.
Alternatively a user could manually pay invoices (tbd).
