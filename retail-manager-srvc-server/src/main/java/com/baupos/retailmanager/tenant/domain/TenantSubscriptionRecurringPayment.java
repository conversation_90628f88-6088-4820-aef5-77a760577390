package com.baupos.retailmanager.tenant.domain;

import com.baupos.retailmanager.common.domain.DatedEntity;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TenantId;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * The recurring payment as setup on the payment gateway. This is not an actual payment.
 * The payment gateway will initiate monthly payments based off this.
 */
@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class TenantSubscriptionRecurringPayment extends DatedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @TenantId
    @Column(nullable = false)
    private UUID tenantId;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "tenant_subscription_id", nullable = false)
    private TenantSubscription subscription;

    @Column(nullable = false)
    private BigDecimal amount;

    @Column(nullable = false)
    private OffsetDateTime nextPaymentDate;

    @Column(nullable = false)
    private String reason;

    @Column(nullable = false)
    private String externalStatus;

    @Column(nullable = false)
    private String externalId;

    @Column(nullable = false)
    private String externalPayerId;

    @Column(nullable = false)
    private String externalPayerEmail;
}
