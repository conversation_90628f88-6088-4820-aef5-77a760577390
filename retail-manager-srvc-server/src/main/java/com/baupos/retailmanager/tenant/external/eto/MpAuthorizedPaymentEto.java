package com.baupos.retailmanager.tenant.external.eto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Getter
@Builder
@AllArgsConstructor
public class MpAuthorizedPaymentEto {
    private final long id;
    private final String type;
    private final String status;
    private final String reason;
    private final Payment payment;
    private final BigDecimal transactionAmount;
    private final String summarized;
    private final String preapprovalId;
    private final Long externalReference;
    private final String currencyId;
    private final OffsetDateTime debitDate;
    private final Integer retryAttempt;
    private final OffsetDateTime dateCreated;
    private final OffsetDateTime lastModified;

    @Getter
    @Builder
    @AllArgsConstructor
    public static class Payment {
        private final Long id;
        private final String status;
        private final String statusDetail;
    }
}