package com.baupos.retailmanager.tenant.domain;

import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class TenantInvoiceItem {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(targetEntity = TenantInvoice.class, fetch = FetchType.LAZY)
    @JoinColumn(nullable = false, name = "tenant_invoice_id")
    private TenantInvoice invoice;

    @Column(nullable = false)
    private String itemName;

    @Column(name = "from_date", nullable = false)
    private OffsetDateTime from;

    @Column(name = "to_date", nullable = false)
    private OffsetDateTime to;

    @Column(nullable = false)
    private BigDecimal quantity;

    @Column(nullable = false)
    private BigDecimal price;

    public BigDecimal getItemTotal() {
        return price.multiply(quantity);
    }
}
