package com.baupos.retailmanager.tenant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Getter
@Setter
@Builder
public class TenantInvoiceItemDto {
    private String itemName;
    private OffsetDateTime from;
    private OffsetDateTime to;
    private BigDecimal quantity;
    private BigDecimal price;

    @JsonProperty
    public BigDecimal getItemTotal() {
        return price.multiply(quantity);
    }
}
