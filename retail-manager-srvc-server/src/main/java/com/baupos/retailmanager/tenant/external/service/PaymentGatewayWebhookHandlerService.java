package com.baupos.retailmanager.tenant.external.service;

import com.baupos.retailmanager.tenant.external.eto.MpAuthorizedPaymentEto;
import com.baupos.retailmanager.tenant.external.eto.PaymentGatewayWebhookRequestEto;
import com.baupos.retailmanager.tenant.service.TenantInvoiceService;
import com.baupos.retailmanager.tenant.service.TenantSubscriptionService;
import com.baupos.retailmanager.tenant.service.sto.TenantInvoiceSto;
import com.baupos.retailmanager.tenant.service.sto.TenantSubscriptionSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentGatewayWebhookHandlerService {

    /**
     * Webhook type for receiving notifications about payments in general.
     */
    private static final String PAYMENT_WEBHOOK_TYPE = "payment";

    /**
     * Webhook type for receiving notifications about subscription-based payments.
     */
    private static final String SUBSCRIPTION_AUTHORIZED_PAYMENT_WEBHOOK_TYPE = "subscription_authorized_payment";

    /**
     * Webhook type for receiving notifications about subscriptions (preapproval) lifecycle.
     */
    private static final String SUBSCRIPTION_PREAPPROVAL_WEBHOOK_TYPE = "subscription_preapproval";

    private final PaymentGatewayService paymentGatewayService;
    private final TenantSubscriptionService tenantSubscriptionService;
    private final TenantInvoiceService tenantInvoiceService;

    public void handleWebhook(PaymentGatewayWebhookRequestEto webhook) {
        log.info("Processing payment gateway webhook {}", webhook);
        switch(webhook.getType()) {
            case SUBSCRIPTION_AUTHORIZED_PAYMENT_WEBHOOK_TYPE:
                processAuthorizedPaymentWebhook(webhook);
            case PAYMENT_WEBHOOK_TYPE:
                processPaymentWebhook(webhook);
            default:
                log.info("Ignoring unsupported webhook {}", webhook);
        }
    }


    private void processPaymentWebhook(PaymentGatewayWebhookRequestEto webhook) {
        // TBD
    }

    private void processAuthorizedPaymentWebhook(PaymentGatewayWebhookRequestEto webhook) {
        MpAuthorizedPaymentEto paymentEto = paymentGatewayService.getAuthorizedPayment(webhook.getData().getId());
        Optional<TenantSubscriptionSto> subscription = tenantSubscriptionService
                .findByRecurringPaymentExternalId(paymentEto.getPreapprovalId());
        if (subscription.isEmpty()) {
            log.warn("Received subscription_authorized_payment webhook but found no matching subscription {}", webhook);
            return;
        }

        Optional<TenantInvoiceSto> matchedPendingInvoice = tenantInvoiceService
                .findPendingBySubscriptionIdAndIssueDateAfterAndTotal(subscription.get().getId(),
                        getStartOfMonth(paymentEto.getDateCreated()), paymentEto.getTransactionAmount());

        if (matchedPendingInvoice.isEmpty()) {
            log.warn("Received subscription_authorized_payment webhook but found no matching invoice on subscription {}",
                    subscription.get());
            return;
        }

        tenantInvoiceService.recordInvoicePayment(matchedPendingInvoice.get().getId(), paymentEto);
    }

    private OffsetDateTime getStartOfMonth(OffsetDateTime date) {
        return date.with(TemporalAdjusters.firstDayOfMonth())
                .toLocalDate()
                .atStartOfDay(date.getOffset())
                .toOffsetDateTime();
    }

}
