package com.baupos.retailmanager.tenant;

import com.baupos.retailmanager.auth.service.*;
import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.tenant.dto.CreateSubscriptionRequestDto;
import com.baupos.retailmanager.tenant.dto.CreateSubscriptionResponseDto;
import com.baupos.retailmanager.tenant.service.TenantService;
import com.baupos.retailmanager.tenant.service.sto.TenantSto;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.service.UserMapper;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.CreateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/onboarding")
public class OnboardingController {

    private final AuthenticationCookieManager authenticationCookieManager;
    private final AuthenticationFacade authenticationFacade;
    private final AuthUserDetailsService authUserDetailsService;
    private final TenantService tenantService;
    private final UserService userService;
    private final TransactionTaskRunner transactionTaskRunner;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final UserMapper userMapper;

    @Value("${sslEnabled:false}")
    private boolean sslEnabled;

    @PostMapping("/subscription")
    public ResponseEntity<CreateSubscriptionResponseDto> createSubscription(HttpServletRequest httpRequest,
                                                                            HttpServletResponse httpResponse,
                                                                            @Valid @RequestBody CreateSubscriptionRequestDto createSubscriptionRequestDto) {
        if (authenticationFacade.getAuthenticatedUserDetails().isPresent()) {
            throw new CmBadRequestException(CmErrorCode.OPERATION_NOT_SUPPORTED_FOR_EXISTING_USERS.details());
        }

        UserSto newUser = transactionTaskRunner.readWriteAndGet(() -> {
            TenantSto newTenant = tenantService.createTenantAndTrialSubscription();
            return userService.createUser(CreateUserRequestSto.builder()
                    .username(createSubscriptionRequestDto.getUsername())
                    .password(passwordEncoder.encode(createSubscriptionRequestDto.getPassword()))
                    .role(Role.OWNER)
                    .privileges(Set.of(Privilege.TENANT_MANAGEMENT))
                    .tenantId(newTenant.getId())
                    .build());
        });

        AuthUserDetailsSto userDetails = authUserDetailsService.loadUserByUsername(createSubscriptionRequestDto.getUsername());
        String accessToken = jwtUtil.generateToken(userDetails);

        AccessTokenAuthentication authentication = new AccessTokenAuthentication(userDetails, null, userDetails.getAuthorities(), accessToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        authenticationCookieManager.setAccessTokenCookie(httpResponse, sslEnabled, accessToken, jwtUtil.getTokenMaxAgeSeconds());

        return ResponseEntity.ok(CreateSubscriptionResponseDto.builder()
                .user(userMapper.mapToDto(newUser))
                .accessToken(accessToken)
                .build());
    }

}
