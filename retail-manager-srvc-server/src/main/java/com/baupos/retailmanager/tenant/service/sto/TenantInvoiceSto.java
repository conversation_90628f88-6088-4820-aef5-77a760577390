package com.baupos.retailmanager.tenant.service.sto;

import com.baupos.retailmanager.tenant.domain.enums.TenantInvoiceStatus;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Builder
@Getter
public class TenantInvoiceSto {

    private UUID id;
    private UUID tenantId;
    private TenantInvoiceStatus status;
    private OffsetDateTime issueDate;
    private OffsetDateTime dueDate;
    @Builder.Default
    private List<TenantInvoiceItemSto> items = new ArrayList<>();


    public BigDecimal getInvoiceTotal() {
        return items.stream().map(TenantInvoiceItemSto::getItemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
