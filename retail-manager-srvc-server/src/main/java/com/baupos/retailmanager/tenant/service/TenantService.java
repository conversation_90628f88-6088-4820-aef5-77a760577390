package com.baupos.retailmanager.tenant.service;

import com.baupos.retailmanager.tenant.domain.Tenant;
import com.baupos.retailmanager.tenant.domain.TenantRepository;
import com.baupos.retailmanager.tenant.domain.TenantSubscription;
import com.baupos.retailmanager.tenant.domain.TenantSubscriptionRepository;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionCode;
import com.baupos.retailmanager.tenant.domain.enums.TenantSubscriptionStatus;
import com.baupos.retailmanager.tenant.service.sto.TenantSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class TenantService {

    private final TenantRepository tenantRepository;
    private final TenantSubscriptionRepository tenantSubscriptionRepository;
    private final TenantMapper tenantMapper;

    @Transactional
    public TenantSto createTenantAndTrialSubscription() {
        Tenant tenant = tenantRepository.save(Tenant.builder()
                .build());

        tenantSubscriptionRepository.save(TenantSubscription.builder()
                .tenantId(tenant.getId())
                .code(TenantSubscriptionCode.STARTER)
                .status(TenantSubscriptionStatus.TRIAL)
                .build());

        return tenantMapper.map(tenant);
    }

}
