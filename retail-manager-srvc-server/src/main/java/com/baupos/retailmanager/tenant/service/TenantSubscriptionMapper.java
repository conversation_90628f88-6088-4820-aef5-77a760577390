package com.baupos.retailmanager.tenant.service;

import com.baupos.retailmanager.tenant.domain.TenantInvoice;
import com.baupos.retailmanager.tenant.domain.TenantInvoiceItem;
import com.baupos.retailmanager.tenant.domain.TenantSubscription;
import com.baupos.retailmanager.tenant.dto.TenantInvoiceDto;
import com.baupos.retailmanager.tenant.dto.TenantInvoiceItemDto;
import com.baupos.retailmanager.tenant.dto.TenantSubscriptionDto;
import com.baupos.retailmanager.tenant.service.sto.TenantInvoiceItemSto;
import com.baupos.retailmanager.tenant.service.sto.TenantInvoiceSto;
import com.baupos.retailmanager.tenant.service.sto.TenantSubscriptionSto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

@Service
public class TenantSubscriptionMapper {

    @Transactional(propagation = Propagation.REQUIRED)
    public TenantSubscriptionSto mapToSto(TenantSubscription tenantSubscription) {
        return TenantSubscriptionSto.builder()
                .id(tenantSubscription.getId())
                .tenantId(tenantSubscription.getTenantId())
                .code(tenantSubscription.getCode())
                .status(tenantSubscription.getStatus())
                .activationDate(tenantSubscription.getActivationDate())
                .createDate(tenantSubscription.getCreateDate())
                .invoices(tenantSubscription.getInvoices().stream().map(this::mapToSto).toList())
                .build();
    }

    public TenantSubscriptionDto mapToDto(TenantSubscriptionSto tenantSubscription) {
        return TenantSubscriptionDto.builder()
                .id(tenantSubscription.getId())
                .code(tenantSubscription.getCode())
                .status(tenantSubscription.getStatus())
                .createDate(tenantSubscription.getCreateDate())
                .invoices(tenantSubscription.getInvoices().stream().map(this::mapToDto).toList())
                .build();
    }

    public TenantInvoiceSto mapToSto(TenantInvoice tenantInvoice) {
        return TenantInvoiceSto.builder()
                .id(tenantInvoice.getId())
                .tenantId(tenantInvoice.getTenantId())
                .status(tenantInvoice.getStatus())
                .issueDate(tenantInvoice.getIssueDate())
                .dueDate(tenantInvoice.getDueDate())
                .items(tenantInvoice.getItems()
                        .stream()
                        .map(item -> mapToItemSto(item))
                        .collect(Collectors.toList()))
                .build();
    }

    public TenantInvoiceDto mapToDto(TenantInvoiceSto tenantInvoiceSto) {
        return TenantInvoiceDto.builder()
                .id(tenantInvoiceSto.getId())
                .tenantId(tenantInvoiceSto.getTenantId())
                .status(tenantInvoiceSto.getStatus())
                .issueDate(tenantInvoiceSto.getIssueDate())
                .dueDate(tenantInvoiceSto.getDueDate())
                .items(tenantInvoiceSto.getItems()
                        .stream()
                        .map(item -> mapToItemDto(item))
                        .collect(Collectors.toList()))
                .build();
    }

    private TenantInvoiceItemSto mapToItemSto(TenantInvoiceItem invoiceItem) {
        return TenantInvoiceItemSto.builder()
                .itemName(invoiceItem.getItemName())
                .from(invoiceItem.getFrom())
                .to(invoiceItem.getTo())
                .price(invoiceItem.getPrice())
                .quantity(invoiceItem.getQuantity())
                .build();
    }

    private TenantInvoiceItemDto mapToItemDto(TenantInvoiceItemSto invoiceItemSto) {
        return TenantInvoiceItemDto.builder()
                .itemName(invoiceItemSto.getItemName())
                .from(invoiceItemSto.getFrom())
                .to(invoiceItemSto.getTo())
                .price(invoiceItemSto.getPrice())
                .quantity(invoiceItemSto.getQuantity())
                .build();
    }
}
