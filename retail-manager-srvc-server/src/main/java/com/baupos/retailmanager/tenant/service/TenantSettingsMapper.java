package com.baupos.retailmanager.tenant.service;

import com.baupos.retailmanager.tenant.domain.TenantSettings;
import com.baupos.retailmanager.tenant.dto.TenantSettingsDto;
import com.baupos.retailmanager.tenant.service.sto.TenantSettingsSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class TenantSettingsMapper {

    @Transactional(propagation = Propagation.MANDATORY)
    public TenantSettingsSto map(TenantSettings tenantSettings) {
        return TenantSettingsSto.builder()
                .tenantId(tenantSettings.getTenantId())
                .companyName(tenantSettings.getCompanyName())
                .locale(tenantSettings.getLocale())
                .timezone(tenantSettings.getTimezone())
                .emailAddress(tenantSettings.getEmailAddress())
                .phoneNumber(tenantSettings.getPhoneNumber())
                .country(tenantSettings.getCountry())
                .city(tenantSettings.getCity())
                .address(tenantSettings.getAddress())
                .build();
    }

    public TenantSettingsDto mapToDto(TenantSettingsSto tenantSettingsSto) {
        return TenantSettingsDto.builder()
                .companyName(tenantSettingsSto.getCompanyName())
                .locale(tenantSettingsSto.getLocale())
                .timezone(tenantSettingsSto.getTimezone())
                .emailAddress(tenantSettingsSto.getEmailAddress())
                .phoneNumber(tenantSettingsSto.getPhoneNumber())
                .country(tenantSettingsSto.getCountry())
                .city(tenantSettingsSto.getCity())
                .address(tenantSettingsSto.getAddress())
                .build();
    }
}
