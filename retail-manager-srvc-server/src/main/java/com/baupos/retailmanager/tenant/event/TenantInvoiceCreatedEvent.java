package com.baupos.retailmanager.tenant.event;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Getter
@Builder
@ToString
public class TenantInvoiceCreatedEvent {
    private final UUID invoiceId;
    private final UUID tenantId;
    private final OffsetDateTime dueDate;
    private final BigDecimal invoiceTotal;
}
