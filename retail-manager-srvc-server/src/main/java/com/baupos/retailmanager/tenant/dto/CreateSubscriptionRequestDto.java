package com.baupos.retailmanager.tenant.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class CreateSubscriptionRequestDto {
    @NotNull
    @Size(min = 4, max = 60)
    private final String username;
    @NotNull
    @Size(min = 4, max = 60)
    private final String password;
}
