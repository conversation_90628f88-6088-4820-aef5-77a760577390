package com.baupos.retailmanager.tenant.service;

import com.baupos.retailmanager.tenant.domain.Tenant;
import com.baupos.retailmanager.tenant.service.sto.TenantSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class TenantMapper {

    @Transactional(propagation = Propagation.MANDATORY)
    public TenantSto map(Tenant tenant) {
        return TenantSto.builder()
                .id(tenant.getId())
                .build();
    }

    public Tenant map(TenantSto tenant) {
        return Tenant.builder()
                .id(tenant.getId())
                .build();
    }

}
