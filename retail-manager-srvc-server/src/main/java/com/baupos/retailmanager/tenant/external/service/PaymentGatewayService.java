package com.baupos.retailmanager.tenant.external.service;

import com.mercadopago.client.payment.PaymentClient;
import com.mercadopago.resources.payment.Payment;
import com.mercadopago.resources.preapproval.Preapproval;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmInternalServerException;
import com.baupos.retailmanager.tenant.external.eto.MpAuthorizedPaymentEto;
import com.baupos.retailmanager.tenant.external.eto.MpCreatePreapprovalRequestEto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

@Service
@RequiredArgsConstructor
public class PaymentGatewayService {

    private final WebClient mercadoPagoClient;

    private final PaymentClient paymentClient;
    private final Clock clock;

    /**
     * Creates a subscription in MercadoPago payment gateway.
     * Payments will be created async by MercadoPago according to the specified recurring details.
     * ref: https://www.mercadopago.com.ar/developers/es/reference/subscriptions/_preapproval/post
     * https://www.mercadopago.com.ar/developers/en/docs/subscriptions/integration-customization/payment-methods/proportional-amount
     *
     * @param amount            Subscription amount (ex. 100.00)
     * @param cardToken         Card token created on the FE
     * @param email             Client email
     * @param externalReference
     * @return Preapproval object with subscription details
     */
    @Transactional(propagation = Propagation.NEVER)
    public Preapproval createMonthlyRecurringPayment(BigDecimal amount, String cardToken, String email,
                                                     String externalReference, String reason) {
        MpCreatePreapprovalRequestEto.MpAutoRecurringDetails autoRecurring =
                MpCreatePreapprovalRequestEto.MpAutoRecurringDetails.builder()
                        .transactionAmount(amount)
                        .startDate(OffsetDateTime.now(clock))
                        .billingDay(1)
                        .billingDayProportional(true)
                        .build();

        MpCreatePreapprovalRequestEto request = MpCreatePreapprovalRequestEto.builder()
                .payerEmail(email)
                .cardTokenId(cardToken)
                .externalReference(externalReference)
                .reason(reason)
                .autoRecurring(autoRecurring)
                .build();

        return mercadoPagoClient.post()
                .uri("/preapproval")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(Preapproval.class)
                .block();
    }

    /**
     * Get details of a subscription-originated payment.
     * https://www.mercadopago.com.ar/developers/en/reference/subscriptions/_authorized_payments_id/get
     * @param authorizedPaymentId
     * @return
     */
    @Transactional(propagation = Propagation.NEVER)
    public MpAuthorizedPaymentEto getAuthorizedPayment(long authorizedPaymentId) {
        return mercadoPagoClient.get()
                .uri("/authorized_payments/{authorizedPaymentId}", uriBuilder -> uriBuilder.build(authorizedPaymentId))
                .retrieve()
                .bodyToMono(MpAuthorizedPaymentEto.class)
                .block();

    }

    /**
     * Get details of any given payment
     * https://www.mercadopago.com.ar/developers/en/reference/payments/_payments_id/get
     * @param paymentId
     * @return
     */
    @Transactional(propagation = Propagation.NEVER)
    public Payment getPayment(long paymentId) {
        try {
            return paymentClient.get(paymentId);
        } catch (Exception e) {
            throw new CmInternalServerException(CmErrorCode.PAYMENT_GATEWAY_ERROR.details(), e);
        }
    }

}