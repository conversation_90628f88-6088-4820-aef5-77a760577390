package com.baupos.retailmanager.branch.service;

import com.baupos.retailmanager.branch.domain.Branch;
import com.baupos.retailmanager.branch.dto.BranchDto;
import com.baupos.retailmanager.branch.service.sto.BranchSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class BranchMapper {

    @Transactional(propagation = Propagation.MANDATORY)
    public BranchSto map(Branch branchEntity) {
        return BranchSto.builder()
                .id(branchEntity.getId())
                .name(branchEntity.getName())
                .country(branchEntity.getCountry())
                .city(branchEntity.getCity())
                .address(branchEntity.getAddress())
                .phoneNumber(branchEntity.getPhoneNumber())
                .emailAddress(branchEntity.getEmailAddress())
                .isPrimary(branchEntity.getIsPrimary())
                .tenantId(branchEntity.getTenantId())
                .build();
    }

    public BranchDto mapToDto(BranchSto branchSto) {
        return BranchDto.builder()
                .id(branchSto.getId())
                .name(branchSto.getName())
                .country(branchSto.getCountry())
                .city(branchSto.getCity())
                .address(branchSto.getAddress())
                .phoneNumber(branchSto.getPhoneNumber())
                .emailAddress(branchSto.getEmailAddress())
                .isPrimary(branchSto.getIsPrimary())
                .build();
    }
}
