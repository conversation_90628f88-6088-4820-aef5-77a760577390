package com.baupos.retailmanager.branch.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class BranchDto {
    private final UUID id;
    private final String name;
    private final String country;
    private final String city;
    private final String address;
    private final String phoneNumber;
    private final String emailAddress;
    private final Boolean isPrimary;
}
