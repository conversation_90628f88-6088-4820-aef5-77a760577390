package com.baupos.retailmanager.branch.service;

import com.baupos.retailmanager.branch.domain.Branch;
import com.baupos.retailmanager.branch.domain.BranchRepository;
import com.baupos.retailmanager.branch.service.sto.BranchSto;
import com.baupos.retailmanager.branch.service.sto.CreateBranchRequestSto;
import com.baupos.retailmanager.branch.service.sto.FindBranchesRequestSto;
import com.baupos.retailmanager.branch.service.sto.UpdateBranchRequestSto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@RequiredArgsConstructor
public class BranchService {

    private final BranchRepository branchRepository;
    private final BranchMapper branchMapper;

    @Transactional(propagation = Propagation.REQUIRED)
    public BranchSto createBranch(CreateBranchRequestSto createBranchRequest) {
        if (!isBlank(createBranchRequest.getName())
                && branchRepository.findByNameAndTenantId(createBranchRequest.getName(), createBranchRequest.getTenantId()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.BRANCH_ALREADY_EXISTS
                    .details(String.format("name=%s", createBranchRequest.getName())));
        }

        if (!isBlank(createBranchRequest.getEmailAddress())
                && branchRepository.findByEmailAddress(createBranchRequest.getEmailAddress()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.BRANCH_ALREADY_EXISTS
                    .details(String.format("emailAddress=%s", createBranchRequest.getEmailAddress())));
        }

        // If this is marked as primary, ensure no other branch is primary for this tenant
        if (Boolean.TRUE.equals(createBranchRequest.getIsPrimary())) {
            Optional<Branch> existingPrimary = branchRepository.findByTenantIdAndIsPrimaryTrue(createBranchRequest.getTenantId());
            if (existingPrimary.isPresent()) {
                throw new CmBadRequestException(CmErrorCode.BRANCH_ALREADY_EXISTS
                        .details("A primary branch already exists for this tenant"));
            }
        }

        Branch newBranch = branchRepository.save(Branch.builder()
                .name(createBranchRequest.getName())
                .country(createBranchRequest.getCountry())
                .city(createBranchRequest.getCity())
                .address(createBranchRequest.getAddress())
                .phoneNumber(createBranchRequest.getPhoneNumber())
                .emailAddress(createBranchRequest.getEmailAddress())
                .isPrimary(createBranchRequest.getIsPrimary())
                .tenantId(createBranchRequest.getTenantId())
                .build());

        return branchMapper.map(newBranch);
    }

    @Transactional(readOnly = true)
    public Optional<BranchSto> findById(UUID branchId) {
        return branchRepository.findByIdAndDeleteDateIsNull(branchId)
                .map(branchMapper::map);
    }

    @Transactional(readOnly = true)
    public Page<BranchSto> findPage(FindBranchesRequestSto request) {
        Page<Branch> branchPage = branchRepository.findFiltered(request.getNameFilter(), request.getCountryFilter(),
                request.getGrantedBranchIds(),
                PageRequest.of(request.getPageNumber(), request.getPageSize(),
                        Sort.by(request.getSortDirection(), request.getSortBy())));

        return new PageImpl<>(branchPage.getContent().stream().map(branchMapper::map).toList(),
                branchPage.getPageable(), branchPage.getTotalElements());
    }

    @Transactional
    public BranchSto update(UpdateBranchRequestSto request) {
        Branch branch = branchRepository.findByIdAndDeleteDateIsNull(request.getId())
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.BRANCH_NOT_FOUND.details(request.getId())));

        // If this is being marked as primary, ensure no other branch is primary for this tenant
        if (Boolean.TRUE.equals(request.getIsPrimary()) && !Boolean.TRUE.equals(branch.getIsPrimary())) {
            Optional<Branch> existingPrimary = branchRepository.findByTenantIdAndIsPrimaryTrue(branch.getTenantId());
            if (existingPrimary.isPresent()) {
                throw new CmBadRequestException(CmErrorCode.BRANCH_ALREADY_EXISTS
                        .details("A primary branch already exists for this tenant"));
            }
        }

        branch.setName(request.getName());
        branch.setCountry(request.getCountry());
        branch.setCity(request.getCity());
        branch.setAddress(request.getAddress());
        branch.setPhoneNumber(request.getPhoneNumber());
        branch.setEmailAddress(request.getEmailAddress());
        branch.setIsPrimary(request.getIsPrimary());

        Branch updatedBranch = branchRepository.save(branch);
        return branchMapper.map(updatedBranch);
    }

    @Transactional(readOnly = true)
    public Optional<UUID> findTenantId(UUID branchId) {
        return branchRepository.findById(branchId).map(Branch::getTenantId);
    }

    @Transactional
    public void deleteBranch(UUID id) {
        branchRepository.deleteById(id);
    }
}
