package com.baupos.retailmanager.branch.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.UUID;

@Getter
@Builder
@ToString(of = {"name", "tenantId"})
@RequiredArgsConstructor
public class CreateBranchRequestSto {
    private final String name;
    private final String country;
    private final String city;
    private final String address;
    private final String phoneNumber;
    private final String emailAddress;
    private final Boolean isPrimary;
    private final UUID tenantId;
}
