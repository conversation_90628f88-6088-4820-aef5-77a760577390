package com.baupos.retailmanager.branch.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class FindBranchesRequestSto {
    private final Integer pageNumber;
    private final Integer pageSize;
    private final String sortBy;
    private final Sort.Direction sortDirection;
    private final String nameFilter;
    private final String countryFilter;
    private final List<UUID> grantedBranchIds;
}
