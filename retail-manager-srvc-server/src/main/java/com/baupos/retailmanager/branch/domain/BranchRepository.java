package com.baupos.retailmanager.branch.domain;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BranchRepository extends JpaRepository<Branch, UUID> {

    Optional<Branch> findByNameAndTenantId(String name, UUID tenantId);

    Optional<Branch> findByEmailAddress(String emailAddress);

    @Query("""
            select b from Branch b
            where (b.name like %:nameFilter% or :nameFilter is null)
            and (b.country like %:countryFilter% or :countryFilter is null)
            and (:grantedBranchIds is null or b.id in :grantedBranchIds)
            and b.deleteDate is null
            """)
    Page<Branch> findFiltered(@Param("nameFilter") String nameFilter,
                             @Param("countryFilter") String countryFilter,
                             @Param("grantedBranchIds") List<UUID> grantedBranchIds,
                             Pageable pageable);

    Optional<Branch> findByIdAndDeleteDateIsNull(UUID id);

    @Query("""
            update Branch b set b.deleteDate = cast(now() as java.time.OffsetDateTime) where b.id = :id and b.deleteDate is null
            """)
    @Modifying
    void deleteById(@Param("id") UUID id);

    Optional<Branch> findByTenantIdAndIsPrimaryTrue(UUID tenantId);
}
