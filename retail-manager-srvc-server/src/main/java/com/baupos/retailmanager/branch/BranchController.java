package com.baupos.retailmanager.branch;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.auth.service.AuthorizationChecks;
import com.baupos.retailmanager.branch.dto.BranchDto;
import com.baupos.retailmanager.branch.dto.CreateBranchRequestDto;
import com.baupos.retailmanager.branch.dto.UpdateBranchRequestDto;
import com.baupos.retailmanager.branch.service.BranchMapper;
import com.baupos.retailmanager.branch.service.BranchService;
import com.baupos.retailmanager.branch.service.sto.BranchSto;
import com.baupos.retailmanager.branch.service.sto.CreateBranchRequestSto;
import com.baupos.retailmanager.branch.service.sto.FindBranchesRequestSto;
import com.baupos.retailmanager.branch.service.sto.UpdateBranchRequestSto;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/v1/branches")
@RequiredArgsConstructor
@Slf4j
public class BranchController {

    private final BranchService branchService;
    private final BranchMapper branchMapper;
    private final AuthenticationFacade authenticationFacade;
    private final AuthorizationChecks authorizationChecks;

    @PostMapping("/")
    @PreAuthorize("@authorizationChecks.canCreateBranch(#createBranchRequest)")
    public BranchDto createBranch(@Valid @RequestBody CreateBranchRequestDto createBranchRequest) {
        BranchSto newBranch = branchService.createBranch(CreateBranchRequestSto.builder()
                .name(createBranchRequest.getName())
                .country(createBranchRequest.getCountry())
                .city(createBranchRequest.getCity())
                .address(createBranchRequest.getAddress())
                .phoneNumber(createBranchRequest.getPhoneNumber())
                .emailAddress(createBranchRequest.getEmailAddress())
                .isPrimary(createBranchRequest.getIsPrimary())
                .tenantId(authenticationFacade.getAuthenticatedTenantId().get())
                .build());
        return branchMapper.mapToDto(newBranch);
    }

    @GetMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canReadBranch(#id)")
    public BranchDto getBranch(@PathVariable UUID id) {
        return branchMapper.mapToDto(branchService.findById(id)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.BRANCH_NOT_FOUND.details(id))));
    }

    @GetMapping("/")
    @PreAuthorize("@authorizationChecks.canReadBranches()")
    public PageDto<BranchDto> listBranches(
            @RequestParam(value = "pageNumber", required = false, defaultValue = "1") @Min(1) Integer pageNumber,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") @Min(1) @Max(100) Integer pageSize,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createDate") String sortBy,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(value = "filter.name", required = false) String nameFilter,
            @RequestParam(value = "filter.country", required = false) String countryFilter
    ) {
        Page<BranchSto> branches = branchService.findPage(FindBranchesRequestSto.builder()
                .pageNumber(pageNumber - 1)
                .pageSize(pageSize)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .nameFilter(nameFilter)
                .countryFilter(countryFilter)
                .grantedBranchIds(authorizationChecks.getGrantedBranchIds())
                .build());

        return new PageDto<>(branches, branchMapper::mapToDto);
    }

    @PutMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canUpdateBranch(#id, #updateBranchRequest)")
    public BranchDto updateBranch(@PathVariable UUID id, @RequestBody UpdateBranchRequestDto updateBranchRequest) {
        return branchMapper.mapToDto(branchService.update(UpdateBranchRequestSto.builder()
                .id(id)
                .name(updateBranchRequest.getName())
                .country(updateBranchRequest.getCountry())
                .city(updateBranchRequest.getCity())
                .address(updateBranchRequest.getAddress())
                .phoneNumber(updateBranchRequest.getPhoneNumber())
                .emailAddress(updateBranchRequest.getEmailAddress())
                .isPrimary(updateBranchRequest.getIsPrimary())
                .build()));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canDeleteBranch(#id)")
    public ResponseEntity<Void> deleteBranch(@PathVariable UUID id) {
        branchService.deleteBranch(id);
        return ResponseEntity.noContent().build();
    }
}
