package com.baupos.retailmanager.branch.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class UpdateBranchRequestSto {
    private final UUID id;
    private final String name;
    private final String country;
    private final String city;
    private final String address;
    private final String phoneNumber;
    private final String emailAddress;
    private final Boolean isPrimary;
}
