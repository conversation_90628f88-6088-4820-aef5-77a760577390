package com.baupos.retailmanager.branch.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString(of = {"name", "country", "city"})
@RequiredArgsConstructor
public class CreateBranchRequestDto {
    @NotNull
    private final String name;
    @NotNull
    private final String country;
    @NotNull
    private final String city;
    private final String address;
    private final String phoneNumber;
    private final String emailAddress;
    @NotNull
    private final Boolean isPrimary;
}
