package com.baupos.retailmanager.branch.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class BranchSto {
    private final UUID id;
    private final String name;
    private final String country;
    private final String city;
    private final String address;
    private final String phoneNumber;
    private final String emailAddress;
    private final Boolean isPrimary;
    private final UUID tenantId;
}
