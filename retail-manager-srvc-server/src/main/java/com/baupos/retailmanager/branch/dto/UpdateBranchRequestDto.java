package com.baupos.retailmanager.branch.dto;

import jakarta.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString(of = {"name", "country", "city"})
@RequiredArgsConstructor
public class UpdateBranchRequestDto {
    @Nullable
    private final String name;
    @Nullable
    private final String country;
    @Nullable
    private final String city;
    @Nullable
    private final String address;
    @Nullable
    private final String phoneNumber;
    @Nullable
    private final String emailAddress;
    @Nullable
    private final Boolean isPrimary;
}
