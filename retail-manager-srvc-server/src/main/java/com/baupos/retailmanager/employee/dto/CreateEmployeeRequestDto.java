package com.baupos.retailmanager.employee.dto;

import com.baupos.retailmanager.user.domain.Privilege;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.HashSet;
import java.util.Set;

@Getter
@Builder
@ToString(of = {"fistName", "tenantId", "systemAccess"})
@RequiredArgsConstructor
public class CreateEmployeeRequestDto {
    @NotNull
    private final String firstName;
    @NotNull
    private final String lastName;
    @Nullable
    private final String phoneNumber;

    /**
     * Employees might or might not have system access, in case this is true,
     * a User will be created with the provided email address,
     * and an email with a reset password token sent.
     */
    private final boolean systemAccess;

    /**
     * Required when {@link #systemAccess} is true, since this is also used as username.
     */
    @Nullable
    private final String email;

    /**
     * Required when {@link #systemAccess} is true.
     */
    @Nullable
    private final String password;

    /**
     * Required when {@link #systemAccess} is true.
     */
    @Builder.Default
    private final Set<Privilege> privileges = new HashSet<>();

}
