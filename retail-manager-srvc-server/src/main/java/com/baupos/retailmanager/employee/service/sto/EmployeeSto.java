package com.baupos.retailmanager.employee.service.sto;

import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class EmployeeSto {
    private final UUID id;
    private final String firstName;
    private final String lastName;
    private final String phoneNumber;
    private final String email;
    private final UserSto user;
    private final OffsetDateTime createDate;
}
