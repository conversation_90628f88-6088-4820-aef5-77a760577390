package com.baupos.retailmanager.employee.dto;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public class UpdateEmployeeRequestDto {
    @Nullable
    @Size(min = 2, max = 20)
    private final String firstName;
    @Nullable
    @Size(min = 1, max = 30)
    private final String lastName;
    @Nullable
    @Size(min = 5, max = 30)
    private final String phoneNumber;
    @Nullable
    @Size(min = 5, max = 50)
    private final String emailAddress;
}
