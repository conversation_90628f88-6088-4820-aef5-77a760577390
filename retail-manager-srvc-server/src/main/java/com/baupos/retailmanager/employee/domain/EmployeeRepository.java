package com.baupos.retailmanager.employee.domain;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, UUID> {

    Optional<Employee> findByEmail(String email);

    @Query("""
            select e from Employee e
            where e.firstName like %:firstNameFilter% or :firstNameFilter is null and e.deleteDate is null
            """)
    Page<Employee> findFiltered(@Param("firstNameFilter") String firstNameFilter, Pageable pageable);

    Optional<Employee> findByIdAndDeleteDateIsNull(UUID id);

    @Query("""
            update Employee e set e.deleteDate = cast(now() as java.time.OffsetDateTime) where e.id = :id and e.deleteDate is null
            """)
    @Modifying
    void deleteById(@Param("id") UUID id);
}
