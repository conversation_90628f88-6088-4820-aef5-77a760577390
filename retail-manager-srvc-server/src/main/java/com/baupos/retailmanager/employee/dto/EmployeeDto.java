package com.baupos.retailmanager.employee.dto;

import com.baupos.retailmanager.user.dto.UserDto;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class EmployeeDto {
    private final UUID id;
    private final String firstName;
    private final String lastName;
    private final String phoneNumber;
    private final String email;
    private final UserDto user;
    private final OffsetDateTime createDate;
}
