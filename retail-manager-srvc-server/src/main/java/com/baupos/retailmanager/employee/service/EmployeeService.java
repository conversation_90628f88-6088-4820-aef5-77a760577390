package com.baupos.retailmanager.employee.service;

import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.employee.domain.Employee;
import com.baupos.retailmanager.employee.domain.EmployeeRepository;
import com.baupos.retailmanager.employee.service.sto.CreateEmployeeRequestSto;
import com.baupos.retailmanager.employee.service.sto.EmployeeSto;
import com.baupos.retailmanager.employee.service.sto.FindEmployeesRequestSto;
import com.baupos.retailmanager.employee.service.sto.UpdateEmployeeRequestSto;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.domain.User;
import com.baupos.retailmanager.user.domain.UserRepository;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.CreateUserRequestSto;
import com.baupos.retailmanager.user.service.sto.UserSto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@RequiredArgsConstructor
public class EmployeeService {

    private final EmployeeRepository employeeRepository;
    private final UserRepository userRepository;
    private final EmployeeMapper employeeMapper;
    private final UserService userService;

    @Transactional(propagation = Propagation.REQUIRED)
    public EmployeeSto createEmployee(CreateEmployeeRequestSto createEmployeeRequest) {
        if (createEmployeeRequest.isSystemAccess()
                && (isBlank(createEmployeeRequest.getEmail()) || isBlank(createEmployeeRequest.getPassword()))) {
            throw new CmBadRequestException(CmErrorCode.MISSING_ARGUMENTS
                    .details(String.format("email=%s, password=%s", isBlank(createEmployeeRequest.getEmail()),
                            isBlank(createEmployeeRequest.getPassword()))));
        }

        if (!isBlank(createEmployeeRequest.getEmail())
                && employeeRepository.findByEmail(createEmployeeRequest.getEmail()).isPresent()) {
            throw new CmBadRequestException(CmErrorCode.EMPLOYEE_ALREADY_EXISTS
                    .details(createEmployeeRequest.getEmail()));
        }

        Employee newEmployee = employeeRepository.save(Employee.builder()
                .firstName(createEmployeeRequest.getFirstName())
                .lastName(createEmployeeRequest.getLastName())
                .phoneNumber(createEmployeeRequest.getPhoneNumber())
                .email(createEmployeeRequest.getEmail())
                .tenantId(createEmployeeRequest.getTenantId())
                .user(createUser(createEmployeeRequest))
                .build());

        return employeeMapper.map(newEmployee);
    }

    @Transactional(readOnly = true)
    public Page<EmployeeSto> findPage(FindEmployeesRequestSto request) {
        Page<Employee> employeePage = employeeRepository.findFiltered(request.getFirstNameFilter(),
                PageRequest.of(request.getPageNumber(), request.getPageSize(),
                        Sort.by(request.getSortDirection(), request.getSortBy())));

        return new PageImpl<>(employeePage.getContent().stream().map(employeeMapper::map).toList(),
                employeePage.getPageable(), employeePage.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Optional<UUID> findTenantId(UUID employeeId) {
        return employeeRepository.findById(employeeId).map(Employee::getTenantId);
    }

    @Transactional
    public EmployeeSto update(UpdateEmployeeRequestSto request) {
        Employee employee = employeeRepository.findByIdAndDeleteDateIsNull(request.getId())
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.EMPLOYEE_NOT_FOUND.details(request.getId())));

        employee.setFirstName(request.getFirstName());
        employee.setLastName(request.getLastName());
        employee.setPhoneNumber(request.getPhoneNumber());
        employee.setEmail(request.getEmailAddress());

        Employee updatedEmployee = employeeRepository.save(employee);
        return employeeMapper.map(updatedEmployee);
    }

    private User createUser(CreateEmployeeRequestSto createEmployeeRequest) {
        if (!createEmployeeRequest.isSystemAccess()) {
            return null;
        }

        UserSto newUser = userService.createUser(CreateUserRequestSto.builder()
                .username(createEmployeeRequest.getEmail())
                .password(createEmployeeRequest.getPassword())
                .tenantId(createEmployeeRequest.getTenantId())
                .privileges(createEmployeeRequest.getPrivileges())
                .role(Role.EMPLOYEE)
                .build());

        return userRepository.getReferenceById(newUser.getId());
    }

    @Transactional
    public void deleteEmployee(UUID id) {
        employeeRepository.deleteById(id);
    }

    @Transactional(readOnly = true)
    public Optional<EmployeeSto> findById(UUID employeeId) {
        return employeeRepository.findByIdAndDeleteDateIsNull(employeeId)
                .map(employeeMapper::map);
    }
}
