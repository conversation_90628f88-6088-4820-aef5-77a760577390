package com.baupos.retailmanager.employee.service.sto;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class UpdateEmployeeRequestSto {
    private final UUID id;
    private final String firstName;
    private final String lastName;
    private final String phoneNumber;
    private final String emailAddress;
}
