package com.baupos.retailmanager.employee.service;

import com.baupos.retailmanager.employee.domain.Employee;
import com.baupos.retailmanager.employee.dto.EmployeeDto;
import com.baupos.retailmanager.employee.service.sto.EmployeeSto;
import com.baupos.retailmanager.user.service.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class EmployeeMapper {

    private final UserMapper userMapper;

    @Transactional(propagation = Propagation.MANDATORY)
    public EmployeeSto map(Employee employeeEntity) {
        return EmployeeSto.builder()
                .id(employeeEntity.getId())
                .firstName(employeeEntity.getFirstName())
                .lastName(employeeEntity.getLastName())
                .email(employeeEntity.getEmail())
                .phoneNumber(employeeEntity.getPhoneNumber())
                .user(Optional.ofNullable(employeeEntity.getUser()).map(userMapper::map).orElse(null))
                .createDate(employeeEntity.getCreateDate())
                .build();
    }

    public EmployeeDto mapToDto(EmployeeSto employeeSto) {
        return EmployeeDto.builder()
                .id(employeeSto.getId())
                .firstName(employeeSto.getFirstName())
                .lastName(employeeSto.getLastName())
                .email(employeeSto.getEmail())
                .phoneNumber(employeeSto.getPhoneNumber())
                .user(Optional.ofNullable(employeeSto.getUser()).map(userMapper::mapToDto).orElse(null))
                .createDate(employeeSto.getCreateDate())
                .build();
    }
}
