package com.baupos.retailmanager.employee.service.sto;

import com.baupos.retailmanager.user.domain.Privilege;
import jakarta.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Getter
@Builder
@ToString(of = {"fistName", "tenantId", "systemAccess"})
@RequiredArgsConstructor
public class CreateEmployeeRequestSto {
    private final String firstName;
    private final String lastName;
    private final String phoneNumber;
    private final UUID tenantId;

    private final boolean systemAccess;
    private final String email;
    private final String password;
    @Builder.Default
    private final Set<Privilege> privileges = new HashSet<>();
}
