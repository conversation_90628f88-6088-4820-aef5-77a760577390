package com.baupos.retailmanager.employee;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.common.dto.PageDto;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import com.baupos.retailmanager.employee.dto.CreateEmployeeRequestDto;
import com.baupos.retailmanager.employee.dto.EmployeeDto;
import com.baupos.retailmanager.employee.dto.UpdateEmployeeRequestDto;
import com.baupos.retailmanager.employee.service.EmployeeMapper;
import com.baupos.retailmanager.employee.service.EmployeeService;
import com.baupos.retailmanager.employee.service.sto.CreateEmployeeRequestSto;
import com.baupos.retailmanager.employee.service.sto.EmployeeSto;
import com.baupos.retailmanager.employee.service.sto.FindEmployeesRequestSto;
import com.baupos.retailmanager.employee.service.sto.UpdateEmployeeRequestSto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/employees")
public class EmployeeController {

    private final EmployeeService employeeService;
    private final EmployeeMapper employeeMapper;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationFacade authenticationFacade;

    @PostMapping("/")
    @PreAuthorize("@authorizationChecks.canCreateEmployee(#createEmployeeRequest)")
    public EmployeeDto createEmployee(@Valid @RequestBody CreateEmployeeRequestDto createEmployeeRequest) {
        EmployeeSto newEmployee = employeeService.createEmployee(CreateEmployeeRequestSto.builder()
                .firstName(createEmployeeRequest.getFirstName())
                .lastName(createEmployeeRequest.getLastName())
                .email(createEmployeeRequest.getEmail())
                .phoneNumber(createEmployeeRequest.getPhoneNumber())
                .systemAccess(createEmployeeRequest.isSystemAccess())
                .password(Optional.ofNullable(createEmployeeRequest.getPassword())
                        .filter(StringUtils::isNotBlank)
                        .map(passwordEncoder::encode)
                        .orElse(null))
                .privileges(createEmployeeRequest.getPrivileges())
                .tenantId(authenticationFacade.getAuthenticatedTenantId().get())
                .build());
        return employeeMapper.mapToDto(newEmployee);
    }

    @GetMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canReadEmployee(#id)")
    public EmployeeDto getEmployee(@PathVariable UUID id) {
        return employeeMapper.mapToDto(employeeService.findById(id)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.EMPLOYEE_NOT_FOUND.details(id))));
    }

    @GetMapping("/")
    @PreAuthorize("@authorizationChecks.canReadEmployees()")
    public PageDto<EmployeeDto> listEmployees(
            @RequestParam(value = "pageNumber", required = false, defaultValue = "1") @Min(1) Integer pageNumber,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") @Min(1) @Max(100) Integer pageSize,
            @RequestParam(value = "sortBy", required = false, defaultValue = "createDate") String sortBy,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @RequestParam(value = "filter.firstName", required = false) String firstNameFilter
    ) {
        Page<EmployeeSto> employees = employeeService.findPage(FindEmployeesRequestSto.builder()
                .pageNumber(pageNumber - 1)
                .pageSize(pageSize)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .firstNameFilter(firstNameFilter)
                .build());
        return new PageDto<>(employees, employeeMapper::mapToDto);
    }


    @PutMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canUpdateEmployee(#id, #updateEmployeeRequest)")
    public EmployeeDto updateEmployee(@PathVariable UUID id, @RequestBody UpdateEmployeeRequestDto updateEmployeeRequest) {
        return employeeMapper.mapToDto(employeeService.update(UpdateEmployeeRequestSto.builder()
                .id(id)
                .firstName(updateEmployeeRequest.getFirstName())
                .lastName(updateEmployeeRequest.getLastName())
                .emailAddress(updateEmployeeRequest.getEmailAddress())
                .phoneNumber(updateEmployeeRequest.getPhoneNumber())
                .build()));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@authorizationChecks.canDeleteEmployee(#id)")
    public ResponseEntity deleteEmployee(@PathVariable UUID id) {
        employeeService.deleteEmployee(id);
        return ResponseEntity.ok().build();
    }

}
