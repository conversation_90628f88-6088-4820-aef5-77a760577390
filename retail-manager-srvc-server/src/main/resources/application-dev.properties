
spring.datasource.url=*********************************************************************
spring.datasource.username=postgres
spring.datasource.password=

spring.liquibase.enabled=true

app.environment=dev

logging.level.root=INFO
logging.level.org.springframework.web=DEBUG
logging.level.com.baupos.retailmanager=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN

#Open Api 3.0
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.enabled=true

cors.allowedOrigins=http://127.0.0.1:3000,https://localhost:8080,http://localhost:8080,http://localhost:3000

# MercadoPago integration details
mercadopago.accessToken=TEST-4864084970020792-050216-2167f1c329c79916e527c8d502031f46-184868317
mercadopago.baseUrl=no-test-env
