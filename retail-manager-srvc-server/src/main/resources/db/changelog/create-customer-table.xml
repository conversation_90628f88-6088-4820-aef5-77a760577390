<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-customer" author="ggreco" >
        <createTable tableName="customer">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="customer_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="first_name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="varchar"/>
            <column name="phone_number" type="varchar"/>
            <column name="personal_id" type="varchar"/>
            <column name="tax_payer_id" type="varchar"/>
            <column name="gender" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="address" type="varchar"/>

            <column name="delete_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="customer" indexName="customer_first_name_idx">
            <column name="first_name"/>
        </createIndex>

        <createIndex tableName="customer" indexName="customer_last_name_idx">
            <column name="last_name"/>
        </createIndex>

        <createIndex tableName="customer" indexName="customer_tax_payer_id_idx">
            <column name="tax_payer_id"/>
        </createIndex>

        <createIndex tableName="customer" indexName="customer_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>