<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-branch" author="ggreco" >
        <createTable tableName="branch">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="branch_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="city" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="varchar"/>
            <column name="phone_number" type="varchar"/>
            <column name="email_address" type="varchar"/>
            <column name="is_primary" type="boolean">
                <constraints nullable="false"/>
            </column>

            <column name="delete_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="branch" indexName="branch_name_idx">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="branch" indexName="branch_country_idx">
            <column name="country"/>
        </createIndex>

        <createIndex tableName="branch" indexName="branch_city_idx">
            <column name="city"/>
        </createIndex>

        <createIndex tableName="branch" indexName="branch_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>

        <createIndex tableName="branch" indexName="branch_is_primary_idx">
            <column name="is_primary"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
