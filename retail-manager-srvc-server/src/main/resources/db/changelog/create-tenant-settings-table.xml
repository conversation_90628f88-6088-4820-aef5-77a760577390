<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-tenant-settings" author="ggreco" >
        <createTable tableName="tenant_settings">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_settings_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true" unique="true"/>
            </column>
            <column name="company_name" type="varchar"/>
            <column name="locale" type="varchar"/>
            <column name="timezone" type="varchar"/>
            <column name="email_address" type="varchar"/>
            <column name="phone_number" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="address" type="varchar"/>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="tenant_settings" indexName="tenant_settings_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
