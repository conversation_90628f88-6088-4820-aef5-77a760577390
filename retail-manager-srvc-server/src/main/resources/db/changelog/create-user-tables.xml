<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-user" author="ggreco" >
        <createTable tableName="user">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="user_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="username" type="varchar">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="password" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="role" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="profile_picture_key" type="varchar"/>

            <column name="delete_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="user" unique="true" indexName="user_username_uidx">
            <column name="username"/>
        </createIndex>

        <createTable tableName="user_privilege">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" foreignKeyName="user_privilege_user_id_fk" referencedTableName="user" referencedColumnNames="id" deleteCascade="true" />
            </column>
            <column name="privilege" type="varchar">
                <constraints nullable="false"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="user_privilege" indexName="user_privilege_user_id_idx">
            <column name="user_id"/>
        </createIndex>

        <createTable tableName="user_granted_resource">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true" unique="true" />
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" foreignKeyName="user_granted_resource_user_id_fk" referencedTableName="user" referencedColumnNames="id" deleteCascade="true" />
            </column>
            <column name="type" type="varchar">
                <constraints nullable="false" />
            </column>
            <column name="key" type="varchar">
                <constraints nullable="false" />
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="user_granted_resource" indexName="user_granted_resource_user_id_type_key_uidx" unique="true">
            <column name="user_id" />
            <column name="type" />
            <column name="key" />
        </createIndex>

        <createIndex tableName="user_granted_resource" indexName="user_granted_resource_user_id_idx">
            <column name="user_id" />
        </createIndex>

        <rollback>
            <dropTable tableName="user"/>
            <dropTable tableName="user_privilege"/>
            <dropTable tableName="user_granted_resource" cascadeConstraints="true" />
        </rollback>

        <createTable tableName="password_reset_token">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" foreignKeyName="password_reset_token_user_id_fk" referencedTableName="user" referencedColumnNames="id" deleteCascade="true" />
            </column>
            <column name="token" type="varchar">
                <constraints nullable="false"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="password_reset_token" indexName="password_reset_token_user_id_token_idx">
            <column name="user_id" />
            <column name="token" />
        </createIndex>
    </changeSet>
</databaseChangeLog>