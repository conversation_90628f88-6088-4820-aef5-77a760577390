<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-employee" author="ggreco" >
        <createTable tableName="employee">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="employee_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="first_name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="varchar"/>
            <column name="phone_number" type="varchar"/>

            <column name="user_id" type="uuid">
                <constraints nullable="true" foreignKeyName="employee_user_id_fk" referencedTableName="user" referencedColumnNames="id" deleteCascade="true" />
            </column>

            <column name="delete_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="employee" unique="true" indexName="employee_user_id_uidx">
            <column name="user_id"/>
        </createIndex>

        <createIndex tableName="employee" unique="false" indexName="employee_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>