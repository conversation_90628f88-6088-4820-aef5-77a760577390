<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-tenant" author="ggreco">
        <createTable tableName="tenant">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <rollback>
            <dropTable tableName="tenant"/>
        </rollback>
    </changeSet>

    <changeSet id="create-table-tenant-subscription" author="ggreco">
        <createTable tableName="tenant_subscription">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_subscription_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="code" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="activation_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="tenant_subscription" indexName="tenant_subscription_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>

        <createTable tableName="tenant_subscription_recurring_payment">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_subscription_tenant_id_fk"
                             referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="tenant_subscription_id" type="uuid">
                <constraints nullable="false"
                             foreignKeyName="tenant_subscription_recurring_payment_tenant_subscription_id_fk"
                             referencedTableName="tenant_subscription"
                             referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="amount" type="decimal(12,2)">
                <constraints nullable="false"/>
            </column>
            <column name="next_payment_date" type="timestamp with timezone"/>
            <column name="reason" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_status" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_payer_id" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_payer_email" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="tenant_subscription_recurring_payment"
                     indexName="tenant_subscription_recurring_payment_external_id_uidx" unique="true">
            <column name="external_id"/>
        </createIndex>

        <rollback>
            <dropTable tableName="tenant_subscription"/>
            <dropTable tableName="tenant_subscription_recurring_payment"/>
        </rollback>
    </changeSet>

    <changeSet id="create-table-tenant-invoice" author="ggreco">
        <createTable tableName="tenant_invoice">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_invoice_tenant_id_fk" referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="tenant_subscription_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_invoice_tenant_subscription_id_fk" referencedTableName="tenant_subscription" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="status" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="issue_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="due_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="tenant_invoice" indexName="tenant_invoice_tenant_id_idx">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex tableName="tenant_invoice" indexName="tenant_invoice_tenant_subscription_id_idx">
            <column name="tenant_subscription_id"/>
        </createIndex>

        <createTable tableName="tenant_invoice_item">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_invoice_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_invoice_item_tenant_invoice_id_fk" referencedTableName="tenant_invoice" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="item_name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="from_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>
            <column name="to_date" type="timestamp with timezone">
                <constraints nullable="true"/>
            </column>

            <column name="quantity" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="decimal(12,2)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="tenant_invoice_item" indexName="tenant_invoice_item_tenant_invoice_id_idx">
            <column name="tenant_invoice_id"/>
        </createIndex>

        <createTable tableName="tenant_invoice_payment">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_invoice_payment_tenant_id_fk"
                             referencedTableName="tenant" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="tenant_invoice_id" type="uuid">
                <constraints nullable="false" foreignKeyName="tenant_invoice_payment_tenant_invoice_id_fk" referencedTableName="tenant_invoice" referencedColumnNames="id" deleteCascade="true"/>
            </column>
            <column name="external_status" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_reason" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="decimal(12,2)">
                <constraints nullable="false"/>
            </column>

            <column name="tenant_subscription_recurring_payment_id" type="uuid">
                <constraints nullable="true"
                             foreignKeyName="tenant_invoice_payment_tenant_subscription_recurring_payment_id_fk"
                             referencedTableName="tenant_subscription_recurring_payment"
                             referencedColumnNames="id" deleteCascade="false"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <rollback>
            <dropTable tableName="tenant_invoice"/>
            <dropTable tableName="tenant_invoice_item"/>
            <dropTable tableName="tenant_invoice_payment"/>
        </rollback>
    </changeSet>

</databaseChangeLog>