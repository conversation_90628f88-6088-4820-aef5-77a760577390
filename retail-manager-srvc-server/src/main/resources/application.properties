spring.application.name=retail-manager-srvc
server.servlet.context-path=/api

spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyComponentPathImpl
spring.datasource.driverClassName=org.postgresql.Driver

spring.datasource.url=prod_db_url
spring.datasource.username=root
spring.datasource.password=root

spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.enabled=false

# Email configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password="uygy gplr sdth zyur"
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
email.from.address=<EMAIL>

app.name=RetailManager

app.environment=prod

jwt.secretSigningKey=cGePuG3B0Bw4oifdK1K62aEvO4QsyOxZofjc63kf4jk956D3msd92jsUwvg70lsf

cors.allowedOrigins=https://www.baupos.com,https://www.baupos.com.ar

#Open Api 3.0
# Disable /api-docs endpoint
springdoc.api-docs.enabled=false
# Disable swagger-ui
springdoc.swagger-ui.enabled=false

# Subscription configurations
subscription.trialDuration=15d
subscription.STARTED.priceMonthly=20000

# Invoice configurations
invoice.dueDate=15d

# MercadoPago integration details
mercadopago.accessToken=tbd
mercadopago.baseUrl=https://api.mercadopago.com/

# S3 configuration
aws.region=us-west-2
aws.s3.bucket-name=baupos-general-prod
aws.credentials.access-key=********************
aws.credentials.secret-key=nsyrl6z/n+10eamwDRy+Wx9oNlMFwsvpRVdte4AF
aws.s3.public-url=https://baupos-general-prod.s3.us-west-2.amazonaws.com/
