spring.datasource.url=jdbc:tc:postgresql:11.4://localhost/testdb?TC_TMPFS=/var/lib/postgresql/data:rw
spring.datasource.username=retailmanager
spring.datasource.password=retailmanager
spring.datasource.driverClassName=org.testcontainers.jdbc.ContainerDatabaseDriver
spring.datasource.initialization-mode=always

logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web.client.RestTemplate=DEBUG

spring.liquibase.enabled=true

app.environment=test

# MercadoPago integration details
mercadopago.accessToken=TEST-4864084970020792-050216-2167f1c329c79916e527c8d502031f46-184868317
mercadopago.baseUrl=no-test-env